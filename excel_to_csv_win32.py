#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用win32com转换Excel文件为CSV
"""

import os
import sys

def convert_excel_with_win32(excel_file, csv_file):
    """使用win32com转换Excel文件"""
    try:
        import win32com.client as win32
        
        print(f"正在使用Excel应用程序转换 {excel_file}...")
        
        # 获取绝对路径
        excel_path = os.path.abspath(excel_file)
        csv_path = os.path.abspath(csv_file)
        
        # 启动Excel应用程序
        excel_app = win32.Dispatch('Excel.Application')
        excel_app.Visible = False
        excel_app.DisplayAlerts = False
        
        # 打开Excel文件
        workbook = excel_app.Workbooks.Open(excel_path)
        worksheet = workbook.ActiveSheet
        
        # 保存为CSV
        workbook.SaveAs(csv_path, FileFormat=6)  # 6 = CSV格式
        
        # 关闭文件和应用程序
        workbook.Close()
        excel_app.Quit()
        
        print(f"  成功转换为: {csv_file}")
        return True
        
    except ImportError:
        print("  win32com模块不可用")
        return False
    except Exception as e:
        print(f"  转换失败: {e}")
        return False

def convert_with_manual_read(excel_file, csv_file):
    """手动读取Excel文件内容"""
    try:
        print(f"正在手动读取 {excel_file}...")
        
        # 尝试使用xlwings
        try:
            import xlwings as xw
            
            app = xw.App(visible=False)
            wb = app.books.open(excel_file)
            ws = wb.sheets[0]
            
            # 获取使用的范围
            used_range = ws.used_range
            values = used_range.value
            
            # 写入CSV文件
            import csv
            with open(csv_file, 'w', newline='', encoding='utf-8-sig') as f:
                writer = csv.writer(f)
                for row in values:
                    writer.writerow(row)
            
            wb.close()
            app.quit()
            
            print(f"  使用xlwings成功转换为: {csv_file}")
            return True
            
        except ImportError:
            print("  xlwings模块不可用")
        except Exception as e:
            print(f"  xlwings转换失败: {e}")
        
        return False
        
    except Exception as e:
        print(f"  手动读取失败: {e}")
        return False

def main():
    """主函数"""
    print("Excel转CSV工具 (使用系统Excel)")
    print("=" * 50)
    
    files_to_convert = [
        ('12.xlsx', '12_fixed_headers.csv'),
        ('13.xlsx', '13_fixed_headers.csv')
    ]
    
    success_count = 0
    
    for excel_file, csv_file in files_to_convert:
        if not os.path.exists(excel_file):
            print(f"文件不存在: {excel_file}")
            continue
        
        print(f"\n处理文件: {excel_file}")
        
        # 方法1: 使用win32com
        if convert_excel_with_win32(excel_file, csv_file):
            success_count += 1
            continue
        
        # 方法2: 使用xlwings
        if convert_with_manual_read(excel_file, csv_file):
            success_count += 1
            continue
        
        print(f"  所有方法都失败了: {excel_file}")
    
    print("\n" + "=" * 50)
    print(f"转换结果: {success_count}/{len(files_to_convert)} 个文件成功")
    
    if success_count > 0:
        print("转换成功的文件可以用于四文件对比分析！")
        
        # 运行四文件对比分析
        print("\n正在运行四文件对比分析...")
        os.system('py four_files_comparison.py')

if __name__ == "__main__":
    main()
