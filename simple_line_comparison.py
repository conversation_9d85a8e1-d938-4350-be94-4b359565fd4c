#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版测试结果折线图对比工具
专门用于生成测试11 vs 测试5的折线图对比
"""

import pandas as pd
import matplotlib.pyplot as plt
import matplotlib
import warnings
warnings.filterwarnings('ignore')

# 设置matplotlib后端和中文字体
matplotlib.use('Agg')  # 使用非交互式后端
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def create_line_plots():
    """创建折线图对比"""
    try:
        print("正在读取数据文件...")
        
        # 读取数据
        df11 = pd.read_csv('11_fixed_headers.csv')
        df5 = pd.read_csv('5_fixed_headers.csv')
        
        print(f"测试11数据: {len(df11)} 个数据点")
        print(f"测试5数据: {len(df5)} 个数据点")
        
        # 温度参数列表
        temp_columns = [
            '遮阳罩表面温度', '遮阳罩背面温度', '遮阳罩皮革表面温度',
            '制冷帐篷表面温度', '制冷帐篷背面温度', '制冷帐篷皮革温度',
            '皮革表面温度', '皮革背面温度', '环境温度'
        ]
        
        print("正在生成完整对比图...")
        
        # 创建3x3子图
        fig, axes = plt.subplots(3, 3, figsize=(20, 16))
        fig.suptitle('测试11 vs 测试5 - 温度对比折线图', fontsize=18, fontweight='bold')
        
        # 设置颜色
        color_11 = '#FF6B6B'  # 红色 - 测试11
        color_5 = '#4ECDC4'   # 青色 - 测试5
        
        for i, col in enumerate(temp_columns):
            row = i // 3
            col_idx = i % 3
            ax = axes[row, col_idx]
            
            # 绘制折线
            ax.plot(df11['时间']/60, df11[col], 
                   color=color_11, linewidth=2, alpha=0.8, 
                   label='测试11')
            ax.plot(df5['时间']/60, df5[col], 
                   color=color_5, linewidth=2, alpha=0.8, 
                   label='测试5')
            
            # 设置标题和标签
            ax.set_title(col, fontsize=12, fontweight='bold')
            ax.set_xlabel('时间 (分钟)', fontsize=10)
            ax.set_ylabel('温度 (°C)', fontsize=10)
            ax.legend(fontsize=9)
            ax.grid(True, alpha=0.3)
            
            # 添加平均值差异信息
            avg_11 = df11[col].mean()
            avg_5 = df5[col].mean()
            diff = avg_11 - avg_5
            ax.text(0.02, 0.98, f'平均差: {diff:+.1f}°C', 
                   transform=ax.transAxes, fontsize=8,
                   bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.7),
                   verticalalignment='top')
        
        plt.tight_layout()
        plt.subplots_adjust(top=0.93)
        
        # 保存图片
        plt.savefig('complete_comparison.png', dpi=300, bbox_inches='tight')
        print("完整对比图已保存: complete_comparison.png")
        plt.close()
        
        # 创建关键参数对比图
        print("正在生成关键参数对比图...")
        
        key_params = ['环境温度', '遮阳罩表面温度', '制冷帐篷表面温度', '皮革表面温度']
        
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        fig.suptitle('关键温度参数对比 - 测试11 vs 测试5', fontsize=16, fontweight='bold')
        
        for i, param in enumerate(key_params):
            row = i // 2
            col = i % 2
            ax = axes[row, col]
            
            # 绘制折线
            ax.plot(df11['时间']/60, df11[param], 
                   color=color_11, linewidth=3, alpha=0.9, 
                   label='测试11', marker='o', markersize=1)
            ax.plot(df5['时间']/60, df5[param], 
                   color=color_5, linewidth=3, alpha=0.9, 
                   label='测试5', marker='s', markersize=1)
            
            ax.set_title(param, fontsize=14, fontweight='bold')
            ax.set_xlabel('时间 (分钟)', fontsize=12)
            ax.set_ylabel('温度 (°C)', fontsize=12)
            ax.legend(fontsize=11)
            ax.grid(True, alpha=0.4)
            
            # 添加起始和结束温度信息
            start_11 = df11[param].iloc[0]
            end_11 = df11[param].iloc[-1]
            start_5 = df5[param].iloc[0]
            end_5 = df5[param].iloc[-1]
            
            change_11 = end_11 - start_11
            change_5 = end_5 - start_5
            
            stats_text = f'测试11: {start_11:.1f}→{end_11:.1f}°C ({change_11:+.1f})\n'
            stats_text += f'测试5: {start_5:.1f}→{end_5:.1f}°C ({change_5:+.1f})'
            
            ax.text(0.02, 0.98, stats_text, transform=ax.transAxes, fontsize=9,
                   bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8),
                   verticalalignment='top')
        
        plt.tight_layout()
        plt.subplots_adjust(top=0.93)
        
        plt.savefig('key_comparison.png', dpi=300, bbox_inches='tight')
        print("关键参数对比图已保存: key_comparison.png")
        plt.close()
        
        # 创建制冷效果专项对比
        print("正在生成制冷效果专项对比图...")
        
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(18, 8))
        fig.suptitle('制冷效果专项对比分析', fontsize=16, fontweight='bold')
        
        # 左图：制冷帐篷温度对比
        ax1.plot(df11['时间']/60, df11['制冷帐篷表面温度'], 
                color=color_11, linewidth=3, label='测试11-制冷帐篷表面', alpha=0.9)
        ax1.plot(df11['时间']/60, df11['制冷帐篷背面温度'], 
                color=color_11, linewidth=2, linestyle='--', label='测试11-制冷帐篷背面', alpha=0.7)
        ax1.plot(df5['时间']/60, df5['制冷帐篷表面温度'], 
                color=color_5, linewidth=3, label='测试5-制冷帐篷表面', alpha=0.9)
        ax1.plot(df5['时间']/60, df5['制冷帐篷背面温度'], 
                color=color_5, linewidth=2, linestyle='--', label='测试5-制冷帐篷背面', alpha=0.7)
        
        ax1.set_title('制冷帐篷温度对比', fontsize=14, fontweight='bold')
        ax1.set_xlabel('时间 (分钟)', fontsize=12)
        ax1.set_ylabel('温度 (°C)', fontsize=12)
        ax1.legend(fontsize=10)
        ax1.grid(True, alpha=0.3)
        
        # 右图：皮革温度对比
        ax2.plot(df11['时间']/60, df11['皮革表面温度'], 
                color=color_11, linewidth=3, label='测试11-皮革表面', alpha=0.9)
        ax2.plot(df11['时间']/60, df11['皮革背面温度'], 
                color=color_11, linewidth=2, linestyle='--', label='测试11-皮革背面', alpha=0.7)
        ax2.plot(df5['时间']/60, df5['皮革表面温度'], 
                color=color_5, linewidth=3, label='测试5-皮革表面', alpha=0.9)
        ax2.plot(df5['时间']/60, df5['皮革背面温度'], 
                color=color_5, linewidth=2, linestyle='--', label='测试5-皮革背面', alpha=0.7)
        
        ax2.set_title('皮革温度对比', fontsize=14, fontweight='bold')
        ax2.set_xlabel('时间 (分钟)', fontsize=12)
        ax2.set_ylabel('温度 (°C)', fontsize=12)
        ax2.legend(fontsize=10)
        ax2.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.subplots_adjust(top=0.9)
        
        plt.savefig('cooling_effect_comparison.png', dpi=300, bbox_inches='tight')
        print("制冷效果对比图已保存: cooling_effect_comparison.png")
        plt.close()
        
        print("\n" + "="*60)
        print("所有折线图对比分析完成！")
        print("生成的图表文件：")
        print("1. complete_comparison.png - 完整9参数对比")
        print("2. key_comparison.png - 关键参数对比")
        print("3. cooling_effect_comparison.png - 制冷效果专项对比")
        print("="*60)
        
        return True
        
    except Exception as e:
        print(f"错误: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("开始生成测试11 vs 测试5的折线图对比...")
    success = create_line_plots()
    if success:
        print("折线图生成完成！")
    else:
        print("折线图生成失败，请检查数据文件。")
