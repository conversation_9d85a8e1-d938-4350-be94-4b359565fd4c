<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试5、11、12、13数据对比折线图</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/papaparse@5.3.0/papaparse.min.js"></script>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .chart-container {
            position: relative;
            height: 400px;
            margin-bottom: 40px;
        }
        .chart-row {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            margin-bottom: 30px;
        }
        .chart-wrapper {
            flex: 1;
            min-width: 45%;
            background-color: white;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 1px 5px rgba(0,0,0,0.1);
        }
        .controls {
            margin-bottom: 20px;
            padding: 15px;
            background-color: #f0f8ff;
            border-radius: 8px;
        }
        select, button {
            padding: 8px 12px;
            margin-right: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        button {
            background-color: #4CAF50;
            color: white;
            border: none;
            cursor: pointer;
        }
        button:hover {
            background-color: #45a049;
        }
        .loading {
            text-align: center;
            padding: 20px;
            font-size: 18px;
            color: #666;
        }
        .error {
            color: #d9534f;
            padding: 10px;
            background-color: #f8d7da;
            border-radius: 4px;
            margin-bottom: 15px;
        }
        .info-panel {
            background-color: #e9f7ef;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .color-legend {
            display: flex;
            justify-content: center;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        .legend-item {
            display: flex;
            align-items: center;
            margin: 0 15px;
        }
        .color-box {
            width: 20px;
            height: 20px;
            margin-right: 8px;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>测试5、11、12、13数据对比折线图</h1>
        
        <div class="info-panel">
            <h3>数据说明</h3>
            <p>本页面展示了测试5、11、12、13的温度数据对比折线图。通过选择不同的温度参数，可以查看各测试之间的差异。</p>
        </div>
        
        <div class="color-legend">
            <div class="legend-item">
                <div class="color-box" style="background-color: rgba(75, 192, 192, 0.8);"></div>
                <span>测试5</span>
            </div>
            <div class="legend-item">
                <div class="color-box" style="background-color: rgba(255, 99, 132, 0.8);"></div>
                <span>测试11</span>
            </div>
            <div class="legend-item">
                <div class="color-box" style="background-color: rgba(54, 162, 235, 0.8);"></div>
                <span>测试12</span>
            </div>
            <div class="legend-item">
                <div class="color-box" style="background-color: rgba(255, 159, 64, 0.8);"></div>
                <span>测试13</span>
            </div>
        </div>
        
        <div class="controls">
            <select id="paramSelect">
                <option value="遮阳罩表面温度">遮阳罩表面温度</option>
                <option value="遮阳罩背面温度">遮阳罩背面温度</option>
                <option value="遮阳罩皮革表面温度">遮阳罩皮革表面温度</option>
                <option value="制冷帐篷表面温度">制冷帐篷表面温度</option>
                <option value="制冷帐篷背面温度">制冷帐篷背面温度</option>
                <option value="制冷帐篷皮革温度">制冷帐篷皮革温度</option>
                <option value="皮革表面温度">皮革表面温度</option>
                <option value="皮革背面温度">皮革背面温度</option>
                <option value="环境温度">环境温度</option>
            </select>
            <button id="plotBtn">绘制折线图</button>
            <button id="plotAllBtn">绘制所有参数对比图</button>
        </div>
        
        <div id="errorMsg" class="error" style="display: none;"></div>
        
        <div id="loading" class="loading">正在加载数据，请稍候...</div>
        
        <div id="singleChartContainer" class="chart-container" style="display: none;">
            <canvas id="singleChart"></canvas>
        </div>
        
        <div id="allChartsContainer" style="display: none;">
            <!-- 第一行图表 -->
            <div class="chart-row">
                <div class="chart-wrapper">
                    <canvas id="chart1"></canvas>
                </div>
                <div class="chart-wrapper">
                    <canvas id="chart2"></canvas>
                </div>
            </div>
            
            <!-- 第二行图表 -->
            <div class="chart-row">
                <div class="chart-wrapper">
                    <canvas id="chart3"></canvas>
                </div>
                <div class="chart-wrapper">
                    <canvas id="chart4"></canvas>
                </div>
            </div>
            
            <!-- 第三行图表 -->
            <div class="chart-row">
                <div class="chart-wrapper">
                    <canvas id="chart5"></canvas>
                </div>
                <div class="chart-wrapper">
                    <canvas id="chart6"></canvas>
                </div>
            </div>
            
            <!-- 第四行图表 -->
            <div class="chart-row">
                <div class="chart-wrapper">
                    <canvas id="chart7"></canvas>
                </div>
                <div class="chart-wrapper">
                    <canvas id="chart8"></canvas>
                </div>
            </div>
            
            <!-- 第五行图表 -->
            <div class="chart-row">
                <div class="chart-wrapper">
                    <canvas id="chart9"></canvas>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 数据文件路径
        const dataFiles = {
            '5': '5_fixed_headers.csv',
            '11': '11_fixed_headers.csv',
            '12': '12.xlsx',  // 注意：这里使用Excel文件，实际使用时需要转换为CSV
            '13': '13.xlsx'   // 注意：这里使用Excel文件，实际使用时需要转换为CSV
        };
        
        // 颜色配置
        const colors = {
            '5': 'rgba(75, 192, 192, 0.8)',   // 青绿色
            '11': 'rgba(255, 99, 132, 0.8)',  // 红色
            '12': 'rgba(54, 162, 235, 0.8)',  // 蓝色
            '13': 'rgba(255, 159, 64, 0.8)'   // 橙色
        };
        
        // 温度参数列表
        const tempParams = [
            '遮阳罩表面温度', '遮阳罩背面温度', '遮阳罩皮革表面温度',
            '制冷帐篷表面温度', '制冷帐篷背面温度', '制冷帐篷皮革温度',
            '皮革表面温度', '皮革背面温度', '环境温度'
        ];
        
        // 存储加载的数据
        let loadedData = {};
        let singleChart = null;
        let allCharts = [];
        
        // 页面加载完成后执行
        document.addEventListener('DOMContentLoaded', function() {
            // 加载CSV数据
            loadCSVData();
            
            // 绑定按钮事件
            document.getElementById('plotBtn').addEventListener('click', plotSelectedParam);
            document.getElementById('plotAllBtn').addEventListener('click', plotAllParams);
        });
        
        // 加载CSV数据
        function loadCSVData() {
            const loadingElement = document.getElementById('loading');
            const errorElement = document.getElementById('errorMsg');
            
            // 重置错误信息
            errorElement.style.display = 'none';
            errorElement.textContent = '';
            
            // 显示加载提示
            loadingElement.style.display = 'block';
            
            // 加载计数器
            let loadedCount = 0;
            let errorCount = 0;
            
            // 加载每个CSV文件
            for (const [testId, filePath] of Object.entries(dataFiles)) {
                // 只加载CSV文件
                if (filePath.endsWith('.csv')) {
                    Papa.parse(filePath, {
                        download: true,
                        header: true,
                        dynamicTyping: true,
                        skipEmptyLines: true,
                        complete: function(results) {
                            loadedData[testId] = results.data;
                            loadedCount++;
                            checkAllLoaded(loadedCount, errorCount);
                        },
                        error: function(error) {
                            console.error(`Error loading ${filePath}:`, error);
                            errorCount++;
                            errorElement.style.display = 'block';
                            errorElement.textContent += `无法加载 ${filePath}: ${error.message}\n`;
                            checkAllLoaded(loadedCount, errorCount);
                        }
                    });
                } else {
                    // 对于Excel文件，显示提示信息
                    console.warn(`${filePath} 是Excel文件，需要先转换为CSV格式`);
                    errorElement.style.display = 'block';
                    errorElement.textContent += `${filePath} 是Excel文件，需要先转换为CSV格式。请使用Python脚本转换后再使用。\n`;
                    errorCount++;
                    checkAllLoaded(loadedCount, errorCount);
                }
            }
            
            // 检查是否所有文件都已加载
            function checkAllLoaded(loaded, errors) {
                if (loaded + errors >= Object.keys(dataFiles).filter(id => dataFiles[id].endsWith('.csv')).length) {
                    loadingElement.style.display = 'none';
                    
                    if (loaded > 0) {
                        // 默认绘制第一个参数
                        plotSelectedParam();
                    }
                }
            }
        }
        
        // 绘制选定的参数
        function plotSelectedParam() {
            const paramSelect = document.getElementById('paramSelect');
            const selectedParam = paramSelect.value;
            
            const singleChartContainer = document.getElementById('singleChartContainer');
            const allChartsContainer = document.getElementById('allChartsContainer');
            
            // 显示单个图表容器，隐藏所有图表容器
            singleChartContainer.style.display = 'block';
            allChartsContainer.style.display = 'none';
            
            // 准备数据
            const datasets = [];
            
            for (const [testId, data] of Object.entries(loadedData)) {
                // 提取时间和选定参数的数据
                const times = data.map(row => row['时间'] / 60); // 转换为分钟
                const values = data.map(row => row[selectedParam]);
                
                datasets.push({
                    label: `测试${testId}`,
                    data: values,
                    borderColor: colors[testId],
                    backgroundColor: colors[testId].replace('0.8', '0.1'),
                    borderWidth: 2,
                    pointRadius: 1,
                    pointHoverRadius: 3,
                    tension: 0.1
                });
            }
            
            // 绘制图表
            const ctx = document.getElementById('singleChart').getContext('2d');
            
            // 如果图表已存在，销毁它
            if (singleChart) {
                singleChart.destroy();
            }
            
            // 创建新图表
            singleChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: loadedData['5'] ? loadedData['5'].map(row => row['时间'] / 60) : [],
                    datasets: datasets
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            display: true,
                            text: selectedParam,
                            font: {
                                size: 16,
                                weight: 'bold'
                            }
                        },
                        tooltip: {
                            mode: 'index',
                            intersect: false
                        },
                        legend: {
                            position: 'top',
                        }
                    },
                    scales: {
                        x: {
                            title: {
                                display: true,
                                text: '时间 (分钟)'
                            }
                        },
                        y: {
                            title: {
                                display: true,
                                text: '温度 (°C)'
                            }
                        }
                    }
                }
            });
        }
        
        // 绘制所有参数对比图
        function plotAllParams() {
            const singleChartContainer = document.getElementById('singleChartContainer');
            const allChartsContainer = document.getElementById('allChartsContainer');
            
            // 隐藏单个图表容器，显示所有图表容器
            singleChartContainer.style.display = 'none';
            allChartsContainer.style.display = 'block';
            
            // 销毁现有的图表
            allCharts.forEach(chart => {
                if (chart) {
                    chart.destroy();
                }
            });
            allCharts = [];
            
            // 为每个参数创建图表
            tempParams.forEach((param, index) => {
                const chartId = `chart${index + 1}`;
                const ctx = document.getElementById(chartId).getContext('2d');
                
                // 准备数据
                const datasets = [];
                
                for (const [testId, data] of Object.entries(loadedData)) {
                    // 提取时间和参数数据
                    const times = data.map(row => row['时间'] / 60); // 转换为分钟
                    const values = data.map(row => row[param]);
                    
                    datasets.push({
                        label: `测试${testId}`,
                        data: values,
                        borderColor: colors[testId],
                        backgroundColor: 'transparent',
                        borderWidth: 2,
                        pointRadius: 0,
                        pointHoverRadius: 3,
                        tension: 0.1
                    });
                }
                
                // 创建图表
                const chart = new Chart(ctx, {
                    type: 'line',
                    data: {
                        labels: loadedData['5'] ? loadedData['5'].map(row => row['时间'] / 60) : [],
                        datasets: datasets
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            title: {
                                display: true,
                                text: param,
                                font: {
                                    size: 14,
                                    weight: 'bold'
                                }
                            },
                            legend: {
                                display: false
                            }
                        },
                        scales: {
                            x: {
                                title: {
                                    display: true,
                                    text: '时间 (分钟)'
                                }
                            },
                            y: {
                                title: {
                                    display: true,
                                    text: '温度 (°C)'
                                }
                            }
                        }
                    }
                });
                
                allCharts.push(chart);
            });
        }
    </script>
</body>
</html>
