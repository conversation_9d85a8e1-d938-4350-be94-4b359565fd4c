<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试数据对比分析结果</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            line-height: 1.6;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
            font-size: 28px;
        }
        h2 {
            color: #34495e;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
            margin-top: 40px;
        }
        .summary {
            background-color: #e8f4fd;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
            border-left: 4px solid #3498db;
        }
        .chart-section {
            margin-bottom: 40px;
        }
        .chart-container {
            text-align: center;
            margin-bottom: 30px;
        }
        .chart-container img {
            max-width: 100%;
            height: auto;
            border: 1px solid #ddd;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .chart-title {
            font-size: 18px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 15px;
        }
        .chart-description {
            color: #7f8c8d;
            font-size: 14px;
            margin-top: 10px;
            max-width: 800px;
            margin-left: auto;
            margin-right: auto;
        }
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        .data-table th, .data-table td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        .data-table th {
            background-color: #f8f9fa;
            font-weight: bold;
            color: #2c3e50;
        }
        .data-table tr:nth-child(even) {
            background-color: #f8f9fa;
        }
        .highlight {
            background-color: #d4edda;
            font-weight: bold;
        }
        .note {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 15px;
            border-radius: 5px;
            margin-top: 20px;
        }
        .status {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }
        .status.success {
            background-color: #d4edda;
            color: #155724;
        }
        .status.failed {
            background-color: #f8d7da;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>测试5、11、12、13数据对比分析结果</h1>
        
        <div class="summary">
            <h3>📊 分析概要</h3>
            <p>本次分析成功处理了<strong>测试5</strong>和<strong>测试11</strong>的数据，生成了详细的对比折线图。</p>
            <p><strong>测试12</strong>和<strong>测试13</strong>的Excel文件由于格式问题暂时无法读取，但已生成基于现有数据的对比分析。</p>
        </div>

        <h2>📈 数据概览</h2>
        <table class="data-table">
            <thead>
                <tr>
                    <th>测试编号</th>
                    <th>数据点数量</th>
                    <th>测试时长</th>
                    <th>状态</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>测试5</td>
                    <td>2,424 个</td>
                    <td>403.8 分钟</td>
                    <td><span class="status success">✓ 已分析</span></td>
                </tr>
                <tr>
                    <td>测试11</td>
                    <td>1,376 个</td>
                    <td>229.2 分钟</td>
                    <td><span class="status success">✓ 已分析</span></td>
                </tr>
                <tr>
                    <td>测试12</td>
                    <td>-</td>
                    <td>-</td>
                    <td><span class="status failed">✗ Excel格式问题</span></td>
                </tr>
                <tr>
                    <td>测试13</td>
                    <td>-</td>
                    <td>-</td>
                    <td><span class="status failed">✗ Excel格式问题</span></td>
                </tr>
            </tbody>
        </table>

        <h2>🌡️ 关键参数对比</h2>
        <table class="data-table">
            <thead>
                <tr>
                    <th>参数</th>
                    <th>测试5 (平均值)</th>
                    <th>测试11 (平均值)</th>
                    <th>差值</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>环境温度</td>
                    <td>31.10°C</td>
                    <td>32.68°C</td>
                    <td>+1.58°C</td>
                </tr>
                <tr class="highlight">
                    <td>制冷帐篷表面温度</td>
                    <td>39.22°C</td>
                    <td>44.77°C</td>
                    <td>+5.55°C</td>
                </tr>
                <tr>
                    <td>皮革表面温度</td>
                    <td>39.54°C</td>
                    <td>50.93°C</td>
                    <td>+11.39°C</td>
                </tr>
            </tbody>
        </table>

        <div class="note">
            <strong>💡 关键发现：</strong>测试5在制冷效果方面明显优于测试11，制冷帐篷表面温度平均低5.55°C，皮革表面温度平均低11.39°C。
        </div>

        <h2>📊 对比图表</h2>
        
        <div class="chart-section">
            <div class="chart-container">
                <div class="chart-title">完整温度参数对比图</div>
                <img src="四文件完整对比图.png" alt="完整温度参数对比图">
                <div class="chart-description">
                    展示了所有9个温度参数的完整对比，包括遮阳罩、制冷帐篷、皮革等各部位的温度变化趋势。
                </div>
            </div>
        </div>

        <div class="chart-section">
            <div class="chart-container">
                <div class="chart-title">关键参数对比图</div>
                <img src="四文件关键参数对比图.png" alt="关键参数对比图">
                <div class="chart-description">
                    重点展示环境温度、遮阳罩表面温度、制冷帐篷表面温度、皮革表面温度等4个关键参数的对比。
                </div>
            </div>
        </div>

        <div class="chart-section">
            <div class="chart-container">
                <div class="chart-title">制冷效果专项对比图</div>
                <img src="四文件制冷效果对比图.png" alt="制冷效果专项对比图">
                <div class="chart-description">
                    专门对比制冷帐篷和皮革的表面与背面温度，突出展示制冷效果的差异。
                </div>
            </div>
        </div>

        <h2>🎯 分析结论</h2>
        <div class="summary">
            <h4>制冷效果排序（从好到差）：</h4>
            <ol>
                <li><strong>测试5</strong>：制冷帐篷表面平均温度 39.22°C</li>
                <li><strong>测试11</strong>：制冷帐篷表面平均温度 44.77°C</li>
            </ol>
            
            <h4>主要发现：</h4>
            <ul>
                <li>测试5的制冷效果显著优于测试11</li>
                <li>测试5的测试时间更长（403.8分钟 vs 229.2分钟），数据更充分</li>
                <li>在皮革温度控制方面，测试5也表现更优</li>
            </ul>
        </div>

        <div class="note">
            <strong>📝 说明：</strong>由于测试12和测试13的Excel文件格式问题，暂时无法读取这两个文件的数据。
            如需包含这两个测试的完整对比分析，建议将Excel文件转换为CSV格式后重新运行分析。
        </div>

        <h2>📁 生成的文件</h2>
        <ul>
            <li><strong>四文件完整对比图.png</strong> - 9个参数完整对比</li>
            <li><strong>四文件关键参数对比图.png</strong> - 4个关键参数对比</li>
            <li><strong>四文件制冷效果对比图.png</strong> - 制冷效果专项对比</li>
            <li><strong>四文件对比分析报告.txt</strong> - 详细分析报告</li>
        </ul>
    </div>
</body>
</html>
