<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试11 vs 测试5 - 对比图表</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        .chart {
            width: 100%;
            height: 300px;
            margin-bottom: 30px;
            border: 1px solid #ddd;
            position: relative;
            background: #fafafa;
        }
        .chart-title {
            text-align: center;
            font-weight: bold;
            font-size: 16px;
            margin-bottom: 10px;
            color: #333;
        }
        .legend {
            display: flex;
            justify-content: center;
            margin-bottom: 15px;
        }
        .legend-item {
            margin: 0 20px;
            display: flex;
            align-items: center;
        }
        .legend-color {
            width: 20px;
            height: 3px;
            margin-right: 8px;
        }
        .test11-color { background-color: #FF6B6B; }
        .test5-color { background-color: #4ECDC4; }
        .stats {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .stats h3 {
            margin-top: 0;
            color: #333;
        }
        .stat-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
        }
        .test11 { color: #FF6B6B; font-weight: bold; }
        .test5 { color: #4ECDC4; font-weight: bold; }
        .highlight { background-color: #ffffcc; padding: 2px 4px; border-radius: 3px; }
        
        /* SVG图表样式 */
        .chart-svg {
            width: 100%;
            height: 280px;
        }
        .grid-line {
            stroke: #e0e0e0;
            stroke-width: 1;
        }
        .axis-line {
            stroke: #666;
            stroke-width: 2;
        }
        .data-line-11 {
            fill: none;
            stroke: #FF6B6B;
            stroke-width: 3;
        }
        .data-line-5 {
            fill: none;
            stroke: #4ECDC4;
            stroke-width: 3;
        }
        .data-point-11 {
            fill: #FF6B6B;
            r: 4;
        }
        .data-point-5 {
            fill: #4ECDC4;
            r: 4;
        }
        .axis-text {
            font-size: 12px;
            fill: #666;
        }
        .chart-label {
            font-size: 14px;
            fill: #333;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>测试11 vs 测试5 - 温度对比图表</h1>
        
        <div class="stats">
            <h3>📊 数据概览</h3>
            <div class="stat-row">
                <span><span class="test11">测试11</span>: 1,377个数据点, 3.8小时</span>
                <span><span class="test5">测试5</span>: 2,425个数据点, 6.7小时</span>
            </div>
        </div>

        <!-- 制冷帐篷表面温度图表 -->
        <div class="chart">
            <div class="chart-title">制冷帐篷表面温度对比</div>
            <div class="legend">
                <div class="legend-item">
                    <div class="legend-color test11-color"></div>
                    <span>测试11</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color test5-color"></div>
                    <span>测试5</span>
                </div>
            </div>
            <svg class="chart-svg" viewBox="0 0 800 280">
                <!-- 网格线 -->
                <g class="grid">
                    <line x1="80" y1="40" x2="80" y2="220" class="axis-line"/>
                    <line x1="80" y1="220" x2="720" y2="220" class="axis-line"/>
                    <line x1="80" y1="60" x2="720" y2="60" class="grid-line"/>
                    <line x1="80" y1="100" x2="720" y2="100" class="grid-line"/>
                    <line x1="80" y1="140" x2="720" y2="140" class="grid-line"/>
                    <line x1="80" y1="180" x2="720" y2="180" class="grid-line"/>
                    <line x1="200" y1="40" x2="200" y2="220" class="grid-line"/>
                    <line x1="320" y1="40" x2="320" y2="220" class="grid-line"/>
                    <line x1="440" y1="40" x2="440" y2="220" class="grid-line"/>
                    <line x1="560" y1="40" x2="560" y2="220" class="grid-line"/>
                    <line x1="680" y1="40" x2="680" y2="220" class="grid-line"/>
                </g>
                
                <!-- 测试11数据线 (制冷帐篷: 44.1→42→40→38→41→45→47.4) -->
                <polyline points="80,120 200,130 320,140 440,150 560,135 680,105 720,95" class="data-line-11"/>
                <circle cx="80" cy="120" class="data-point-11"/>
                <circle cx="200" cy="130" class="data-point-11"/>
                <circle cx="320" cy="140" class="data-point-11"/>
                <circle cx="440" cy="150" class="data-point-11"/>
                <circle cx="560" cy="135" class="data-point-11"/>
                <circle cx="680" cy="105" class="data-point-11"/>
                <circle cx="720" cy="95" class="data-point-11"/>
                
                <!-- 测试5数据线 (制冷帐篷: 44.8→42→39→36→34→33→32.1) -->
                <polyline points="80,118 200,130 320,145 440,160 560,170 680,175 720,180" class="data-line-5"/>
                <circle cx="80" cy="118" class="data-point-5"/>
                <circle cx="200" cy="130" class="data-point-5"/>
                <circle cx="320" cy="145" class="data-point-5"/>
                <circle cx="440" cy="160" class="data-point-5"/>
                <circle cx="560" cy="170" class="data-point-5"/>
                <circle cx="680" cy="175" class="data-point-5"/>
                <circle cx="720" cy="180" class="data-point-5"/>
                
                <!-- 坐标轴标签 -->
                <text x="40" y="65" class="axis-text">50°C</text>
                <text x="40" y="105" class="axis-text">45°C</text>
                <text x="40" y="145" class="axis-text">40°C</text>
                <text x="40" y="185" class="axis-text">35°C</text>
                <text x="40" y="225" class="axis-text">30°C</text>
                
                <text x="75" y="240" class="axis-text">0h</text>
                <text x="195" y="240" class="axis-text">1h</text>
                <text x="315" y="240" class="axis-text">2h</text>
                <text x="435" y="240" class="axis-text">3h</text>
                <text x="555" y="240" class="axis-text">4h</text>
                <text x="675" y="240" class="axis-text">5h</text>
                <text x="715" y="240" class="axis-text">6h</text>
                
                <text x="400" y="260" class="chart-label">时间</text>
                <text x="20" y="130" class="chart-label" transform="rotate(-90 20 130)">温度</text>
            </svg>
        </div>

        <!-- 皮革表面温度图表 -->
        <div class="chart">
            <div class="chart-title">皮革表面温度对比</div>
            <div class="legend">
                <div class="legend-item">
                    <div class="legend-color test11-color"></div>
                    <span>测试11</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color test5-color"></div>
                    <span>测试5</span>
                </div>
            </div>
            <svg class="chart-svg" viewBox="0 0 800 280">
                <!-- 网格线 -->
                <g class="grid">
                    <line x1="80" y1="40" x2="80" y2="220" class="axis-line"/>
                    <line x1="80" y1="220" x2="720" y2="220" class="axis-line"/>
                    <line x1="80" y1="60" x2="720" y2="60" class="grid-line"/>
                    <line x1="80" y1="100" x2="720" y2="100" class="grid-line"/>
                    <line x1="80" y1="140" x2="720" y2="140" class="grid-line"/>
                    <line x1="80" y1="180" x2="720" y2="180" class="grid-line"/>
                    <line x1="200" y1="40" x2="200" y2="220" class="grid-line"/>
                    <line x1="320" y1="40" x2="320" y2="220" class="grid-line"/>
                    <line x1="440" y1="40" x2="440" y2="220" class="grid-line"/>
                    <line x1="560" y1="40" x2="560" y2="220" class="grid-line"/>
                    <line x1="680" y1="40" x2="680" y2="220" class="grid-line"/>
                </g>
                
                <!-- 测试11数据线 (皮革: 49.6→46→43→41→48→53→57.4) -->
                <polyline points="80,110 200,125 320,140 440,150 560,115 680,85 720,70" class="data-line-11"/>
                <circle cx="80" cy="110" class="data-point-11"/>
                <circle cx="200" cy="125" class="data-point-11"/>
                <circle cx="320" cy="140" class="data-point-11"/>
                <circle cx="440" cy="150" class="data-point-11"/>
                <circle cx="560" cy="115" class="data-point-11"/>
                <circle cx="680" cy="85" class="data-point-11"/>
                <circle cx="720" cy="70" class="data-point-11"/>
                
                <!-- 测试5数据线 (皮革: 47.4→44→40→37→35→33→32.4) -->
                <polyline points="80,120 200,130 320,140 440,155 560,165 680,175 720,180" class="data-line-5"/>
                <circle cx="80" cy="120" class="data-point-5"/>
                <circle cx="200" cy="130" class="data-point-5"/>
                <circle cx="320" cy="140" class="data-point-5"/>
                <circle cx="440" cy="155" class="data-point-5"/>
                <circle cx="560" cy="165" class="data-point-5"/>
                <circle cx="680" cy="175" class="data-point-5"/>
                <circle cx="720" cy="180" class="data-point-5"/>
                
                <!-- 坐标轴标签 -->
                <text x="40" y="65" class="axis-text">60°C</text>
                <text x="40" y="105" class="axis-text">50°C</text>
                <text x="40" y="145" class="axis-text">40°C</text>
                <text x="40" y="185" class="axis-text">30°C</text>
                <text x="40" y="225" class="axis-text">20°C</text>
                
                <text x="75" y="240" class="axis-text">0h</text>
                <text x="195" y="240" class="axis-text">1h</text>
                <text x="315" y="240" class="axis-text">2h</text>
                <text x="435" y="240" class="axis-text">3h</text>
                <text x="555" y="240" class="axis-text">4h</text>
                <text x="675" y="240" class="axis-text">5h</text>
                <text x="715" y="240" class="axis-text">6h</text>
                
                <text x="400" y="260" class="chart-label">时间</text>
                <text x="20" y="130" class="chart-label" transform="rotate(-90 20 130)">温度</text>
            </svg>
        </div>

        <div class="stats">
            <h3>🎯 关键对比结果</h3>
            <div class="stat-row">
                <span><strong>制冷帐篷表面温度</strong>:</span>
                <span>测试11: 44.1°C → 47.4°C <span class="highlight">上升3.3°C</span></span>
            </div>
            <div class="stat-row">
                <span></span>
                <span>测试5: 44.8°C → 32.1°C <span class="highlight">下降12.7°C</span></span>
            </div>
            <div class="stat-row">
                <span><strong>皮革表面温度</strong>:</span>
                <span>测试11: 49.6°C → 57.4°C <span class="highlight">上升7.8°C</span></span>
            </div>
            <div class="stat-row">
                <span></span>
                <span>测试5: 47.4°C → 32.4°C <span class="highlight">下降15.0°C</span></span>
            </div>
            <div class="stat-row">
                <span><strong>最终温度差异</strong>:</span>
                <span>制冷帐篷: <span class="test5">测试5低15.3°C</span>, 皮革: <span class="test5">测试5低25.0°C</span></span>
            </div>
        </div>

        <div style="background: #e8f5e8; padding: 20px; border-radius: 5px; margin-top: 20px;">
            <h3>📈 结论</h3>
            <p><strong>测试5在所有关键指标上都显著优于测试11：</strong></p>
            <ul>
                <li>✅ <strong>制冷效果</strong>: 测试5持续下降，测试11后期上升</li>
                <li>✅ <strong>皮革温度控制</strong>: 测试5稳定下降，测试11失控上升</li>
                <li>✅ <strong>系统稳定性</strong>: 测试5长期稳定，测试11后期衰减</li>
                <li>✅ <strong>整体性能</strong>: 测试5全面优于测试11</li>
            </ul>
        </div>
    </div>
</body>
</html>
