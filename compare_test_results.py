#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试结果对比分析工具
对比测试11和测试5的数据结果
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from matplotlib import rcParams
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
rcParams['axes.unicode_minus'] = False

def load_and_compare_data():
    """加载并对比两个测试结果"""
    try:
        # 读取数据
        df11 = pd.read_csv('11_fixed_headers.csv')
        df5 = pd.read_csv('5_fixed_headers.csv')
        
        print("=" * 60)
        print("测试结果11 vs 测试结果5 - 详细对比分析")
        print("=" * 60)
        
        # 基本信息对比
        print("\n【基本信息对比】")
        print(f"测试11数据点数量: {len(df11):,} 个")
        print(f"测试5数据点数量:  {len(df5):,} 个")
        print(f"测试11持续时间:   {df11['时间'].max():.1f} 秒 ({df11['时间'].max()/60:.1f} 分钟)")
        print(f"测试5持续时间:    {df5['时间'].max():.1f} 秒 ({df5['时间'].max()/60:.1f} 分钟)")
        
        # 温度参数列表
        temp_columns = [
            '遮阳罩表面温度', '遮阳罩背面温度', '遮阳罩皮革表面温度',
            '制冷帐篷表面温度', '制冷帐篷背面温度', '制冷帐篷皮革温度',
            '皮革表面温度', '皮革背面温度', '环境温度'
        ]
        
        # 平均值对比
        print("\n【平均温度对比】")
        print(f"{'参数名称':<15} {'测试11':<8} {'测试5':<8} {'差值':<8} {'说明'}")
        print("-" * 60)
        for col in temp_columns:
            avg_11 = df11[col].mean()
            avg_5 = df5[col].mean()
            diff = avg_11 - avg_5
            trend = "测试11更高" if diff > 0 else "测试5更高" if diff < 0 else "相等"
            print(f"{col:<15} {avg_11:>7.2f} {avg_5:>7.2f} {diff:>+7.2f} {trend}")
        
        # 最高温度对比
        print("\n【最高温度对比】")
        print(f"{'参数名称':<15} {'测试11':<8} {'测试5':<8} {'差值':<8} {'说明'}")
        print("-" * 60)
        for col in temp_columns:
            max_11 = df11[col].max()
            max_5 = df5[col].max()
            diff = max_11 - max_5
            trend = "测试11更高" if diff > 0 else "测试5更高" if diff < 0 else "相等"
            print(f"{col:<15} {max_11:>7.2f} {max_5:>7.2f} {diff:>+7.2f} {trend}")
        
        # 最低温度对比
        print("\n【最低温度对比】")
        print(f"{'参数名称':<15} {'测试11':<8} {'测试5':<8} {'差值':<8} {'说明'}")
        print("-" * 60)
        for col in temp_columns:
            min_11 = df11[col].min()
            min_5 = df5[col].min()
            diff = min_11 - min_5
            trend = "测试11更高" if diff > 0 else "测试5更高" if diff < 0 else "相等"
            print(f"{col:<15} {min_11:>7.2f} {min_5:>7.2f} {diff:>+7.2f} {trend}")
        
        # 温度变化范围对比
        print("\n【温度变化范围对比】")
        print(f"{'参数名称':<15} {'测试11':<8} {'测试5':<8} {'差值':<8} {'说明'}")
        print("-" * 60)
        for col in temp_columns:
            range_11 = df11[col].max() - df11[col].min()
            range_5 = df5[col].max() - df5[col].min()
            diff = range_11 - range_5
            trend = "测试11波动更大" if diff > 0 else "测试5波动更大" if diff < 0 else "波动相等"
            print(f"{col:<15} {range_11:>7.2f} {range_5:>7.2f} {diff:>+7.2f} {trend}")
        
        # 关键发现总结
        print("\n" + "=" * 60)
        print("【关键发现总结】")
        print("=" * 60)
        
        # 环境温度对比
        env_avg_11 = df11['环境温度'].mean()
        env_avg_5 = df5['环境温度'].mean()
        print(f"1. 环境温度: 测试11平均{env_avg_11:.2f}°C, 测试5平均{env_avg_5:.2f}°C")
        print(f"   差值: {env_avg_11-env_avg_5:+.2f}°C")
        
        # 遮阳罩效果对比
        shade_surface_11 = df11['遮阳罩表面温度'].mean()
        shade_surface_5 = df5['遮阳罩表面温度'].mean()
        print(f"2. 遮阳罩表面温度: 测试11平均{shade_surface_11:.2f}°C, 测试5平均{shade_surface_5:.2f}°C")
        print(f"   差值: {shade_surface_11-shade_surface_5:+.2f}°C")
        
        # 制冷效果对比
        cooling_surface_11 = df11['制冷帐篷表面温度'].mean()
        cooling_surface_5 = df5['制冷帐篷表面温度'].mean()
        print(f"3. 制冷帐篷表面温度: 测试11平均{cooling_surface_11:.2f}°C, 测试5平均{cooling_surface_5:.2f}°C")
        print(f"   差值: {cooling_surface_11-cooling_surface_5:+.2f}°C")
        
        # 皮革温度对比
        leather_surface_11 = df11['皮革表面温度'].mean()
        leather_surface_5 = df5['皮革表面温度'].mean()
        print(f"4. 皮革表面温度: 测试11平均{leather_surface_11:.2f}°C, 测试5平均{leather_surface_5:.2f}°C")
        print(f"   差值: {leather_surface_11-leather_surface_5:+.2f}°C")
        
        return df11, df5, temp_columns
        
    except Exception as e:
        print(f"数据加载错误: {e}")
        return None, None, None

def create_comparison_plots(df11, df5, temp_columns):
    """创建对比图表"""
    try:
        # 设置颜色方案
        colors_11 = '#FF6B6B'  # 红色系 - 测试11
        colors_5 = '#4ECDC4'   # 青色系 - 测试5

        # 创建3x3子图布局
        fig, axes = plt.subplots(3, 3, figsize=(20, 16))
        fig.suptitle('测试11 vs 测试5 - 温度对比折线图', fontsize=18, fontweight='bold', y=0.95)

        for i, col in enumerate(temp_columns):
            row = i // 3
            col_idx = i % 3
            ax = axes[row, col_idx]

            # 绘制两条折线
            line1 = ax.plot(df11['时间']/60, df11[col],
                           color=colors_11, linewidth=2, alpha=0.8,
                           label='测试11', marker='o', markersize=1)
            line2 = ax.plot(df5['时间']/60, df5[col],
                           color=colors_5, linewidth=2, alpha=0.8,
                           label='测试5', marker='s', markersize=1)

            # 设置标题和标签
            ax.set_title(col, fontsize=13, fontweight='bold', pad=10)
            ax.set_xlabel('时间 (分钟)', fontsize=11)
            ax.set_ylabel('温度 (°C)', fontsize=11)

            # 添加图例
            ax.legend(loc='best', fontsize=10, framealpha=0.9)

            # 设置网格
            ax.grid(True, alpha=0.3, linestyle='--')

            # 美化坐标轴
            ax.tick_params(axis='both', which='major', labelsize=9)

            # 添加统计信息文本
            avg_11 = df11[col].mean()
            avg_5 = df5[col].mean()
            diff = avg_11 - avg_5

            # 在图上添加平均值信息
            textstr = f'平均值差: {diff:+.1f}°C'
            props = dict(boxstyle='round', facecolor='wheat', alpha=0.7)
            ax.text(0.02, 0.98, textstr, transform=ax.transAxes, fontsize=9,
                   verticalalignment='top', bbox=props)

        plt.tight_layout()
        plt.subplots_adjust(top=0.92)

        # 保存高分辨率图片
        plt.savefig('test_comparison_line_plots.png', dpi=300, bbox_inches='tight',
                   facecolor='white', edgecolor='none')
        print(f"\n折线图对比已保存为: test_comparison_line_plots.png")

        # 显示图表
        plt.show()

    except Exception as e:
        print(f"图表创建错误: {e}")

def create_key_parameters_comparison(df11, df5):
    """创建关键参数对比图"""
    try:
        # 选择最重要的4个参数进行重点对比
        key_params = ['环境温度', '遮阳罩表面温度', '制冷帐篷表面温度', '皮革表面温度']

        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        fig.suptitle('关键温度参数对比 - 测试11 vs 测试5', fontsize=16, fontweight='bold')

        colors_11 = '#FF6B6B'  # 红色 - 测试11
        colors_5 = '#4ECDC4'   # 青色 - 测试5

        for i, param in enumerate(key_params):
            row = i // 2
            col = i % 2
            ax = axes[row, col]

            # 绘制折线图
            ax.plot(df11['时间']/60, df11[param],
                   color=colors_11, linewidth=2.5, alpha=0.9,
                   label='测试11', marker='o', markersize=2)
            ax.plot(df5['时间']/60, df5[param],
                   color=colors_5, linewidth=2.5, alpha=0.9,
                   label='测试5', marker='s', markersize=2)

            # 设置标题和标签
            ax.set_title(param, fontsize=14, fontweight='bold', pad=15)
            ax.set_xlabel('时间 (分钟)', fontsize=12)
            ax.set_ylabel('温度 (°C)', fontsize=12)

            # 添加图例
            ax.legend(loc='best', fontsize=11, framealpha=0.9)

            # 设置网格
            ax.grid(True, alpha=0.4, linestyle='-', linewidth=0.5)

            # 美化坐标轴
            ax.tick_params(axis='both', which='major', labelsize=10)

            # 计算并显示关键统计信息
            start_11 = df11[param].iloc[0]
            end_11 = df11[param].iloc[-1]
            start_5 = df5[param].iloc[0]
            end_5 = df5[param].iloc[-1]

            change_11 = end_11 - start_11
            change_5 = end_5 - start_5

            # 添加统计信息
            stats_text = f'测试11: {start_11:.1f}°C → {end_11:.1f}°C ({change_11:+.1f}°C)\n'
            stats_text += f'测试5: {start_5:.1f}°C → {end_5:.1f}°C ({change_5:+.1f}°C)'

            props = dict(boxstyle='round', facecolor='lightblue', alpha=0.8)
            ax.text(0.02, 0.98, stats_text, transform=ax.transAxes, fontsize=9,
                   verticalalignment='top', bbox=props)

        plt.tight_layout()
        plt.subplots_adjust(top=0.92)

        # 保存图片
        plt.savefig('key_parameters_comparison.png', dpi=300, bbox_inches='tight',
                   facecolor='white', edgecolor='none')
        print(f"关键参数对比图已保存为: key_parameters_comparison.png")

        plt.show()

    except Exception as e:
        print(f"关键参数图表创建错误: {e}")

def create_summary_comparison(df11, df5):
    """创建汇总对比图"""
    try:
        # 创建一个综合对比图
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(18, 8))
        fig.suptitle('测试结果综合对比', fontsize=16, fontweight='bold')

        # 左图：环境温度和制冷效果对比
        ax1.plot(df11['时间']/60, df11['环境温度'],
                color='orange', linewidth=2, label='测试11-环境温度', alpha=0.8)
        ax1.plot(df11['时间']/60, df11['制冷帐篷表面温度'],
                color='red', linewidth=2, label='测试11-制冷帐篷', alpha=0.8)
        ax1.plot(df5['时间']/60, df5['环境温度'],
                color='lightblue', linewidth=2, label='测试5-环境温度', alpha=0.8)
        ax1.plot(df5['时间']/60, df5['制冷帐篷表面温度'],
                color='blue', linewidth=2, label='测试5-制冷帐篷', alpha=0.8)

        ax1.set_title('环境温度 vs 制冷效果对比', fontsize=14, fontweight='bold')
        ax1.set_xlabel('时间 (分钟)', fontsize=12)
        ax1.set_ylabel('温度 (°C)', fontsize=12)
        ax1.legend(fontsize=10)
        ax1.grid(True, alpha=0.3)

        # 右图：皮革温度对比
        ax2.plot(df11['时间']/60, df11['皮革表面温度'],
                color='red', linewidth=3, label='测试11-皮革表面', alpha=0.9)
        ax2.plot(df11['时间']/60, df11['皮革背面温度'],
                color='pink', linewidth=2, label='测试11-皮革背面', alpha=0.7)
        ax2.plot(df5['时间']/60, df5['皮革表面温度'],
                color='blue', linewidth=3, label='测试5-皮革表面', alpha=0.9)
        ax2.plot(df5['时间']/60, df5['皮革背面温度'],
                color='lightblue', linewidth=2, label='测试5-皮革背面', alpha=0.7)

        ax2.set_title('皮革温度对比', fontsize=14, fontweight='bold')
        ax2.set_xlabel('时间 (分钟)', fontsize=12)
        ax2.set_ylabel('温度 (°C)', fontsize=12)
        ax2.legend(fontsize=10)
        ax2.grid(True, alpha=0.3)

        plt.tight_layout()
        plt.subplots_adjust(top=0.9)

        # 保存图片
        plt.savefig('summary_comparison.png', dpi=300, bbox_inches='tight',
                   facecolor='white', edgecolor='none')
        print(f"综合对比图已保存为: summary_comparison.png")

        plt.show()

    except Exception as e:
        print(f"综合对比图创建错误: {e}")

def main():
    """主函数"""
    print("正在加载和分析测试数据...")

    # 加载和对比数据
    df11, df5, temp_columns = load_and_compare_data()

    if df11 is not None and df5 is not None:
        print("\n正在生成折线图对比...")

        # 创建完整的9参数对比图表
        create_comparison_plots(df11, df5, temp_columns)

        # 创建关键参数对比图
        create_key_parameters_comparison(df11, df5)

        # 创建综合对比图
        create_summary_comparison(df11, df5)

        print("\n" + "=" * 60)
        print("所有折线图对比分析完成！")
        print("生成的图表文件：")
        print("1. test_comparison_line_plots.png - 完整9参数对比")
        print("2. key_parameters_comparison.png - 关键参数对比")
        print("3. summary_comparison.png - 综合对比")
        print("=" * 60)
    else:
        print("数据加载失败，请检查文件是否存在。")

if __name__ == "__main__":
    main()
