#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一时间轴对比分析工具
将所有四个测试的数据在同一个时间轴上进行对比
"""

import pandas as pd
import matplotlib.pyplot as plt
import matplotlib
import numpy as np
import os
import warnings
warnings.filterwarnings('ignore')

# 设置matplotlib后端和中文字体
matplotlib.use('Agg')  # 使用非交互式后端
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def load_and_prepare_data():
    """加载数据并准备统一时间轴"""
    print("正在加载数据并准备统一时间轴...")
    
    # 文件映射
    files = {
        '5': '5_fixed_headers.csv',
        '11': '11_fixed_headers.csv',
        '12': '12_fixed_headers.csv',
        '13': '13_fixed_headers.csv'
    }
    
    data = {}
    max_time = 0
    
    # 加载所有数据
    for test_id, filename in files.items():
        if os.path.exists(filename):
            try:
                df = pd.read_csv(filename)
                data[test_id] = df
                test_max_time = df['时间'].max() / 60  # 转换为分钟
                max_time = max(max_time, test_max_time)
                print(f"✓ 测试{test_id}: {len(df)} 个数据点, 最大时间: {test_max_time:.1f} 分钟")
            except Exception as e:
                print(f"✗ 测试{test_id}: 加载失败 - {e}")
    
    print(f"\n统一时间轴范围: 0 - {max_time:.1f} 分钟")
    return data, max_time

def create_unified_timeline_charts(data, max_time):
    """创建统一时间轴的对比图表"""
    
    # 温度参数列表
    temp_columns = [
        '遮阳罩表面温度', '遮阳罩背面温度', '遮阳罩皮革表面温度',
        '制冷帐篷表面温度', '制冷帐篷背面温度', '制冷帐篷皮革温度',
        '皮革表面温度', '皮革背面温度', '环境温度'
    ]
    
    # 颜色配置
    colors = {
        '5': '#4ECDC4',   # 青绿色
        '11': '#FF6B6B',  # 红色
        '12': '#45B7D1',  # 蓝色
        '13': '#FFA07A'   # 橙色
    }
    
    # 线型配置
    line_styles = {
        '5': '-',      # 实线
        '11': '--',    # 虚线
        '12': '-.',    # 点划线
        '13': ':'      # 点线
    }
    
    # 1. 创建统一时间轴完整对比图 (3x3)
    print("正在生成统一时间轴完整对比图...")
    
    fig, axes = plt.subplots(3, 3, figsize=(24, 18))
    fig.suptitle('统一时间轴 - 四测试完整温度对比折线图', fontsize=20, fontweight='bold')
    
    for i, col in enumerate(temp_columns):
        row = i // 3
        col_idx = i % 3
        ax = axes[row, col_idx]
        
        # 绘制每个测试的折线
        for test_id, df in data.items():
            if col in df.columns:
                time_minutes = df['时间'] / 60  # 转换为分钟
                ax.plot(time_minutes, df[col], 
                       color=colors[test_id], 
                       linestyle=line_styles[test_id],
                       linewidth=2.5, alpha=0.8, 
                       label=f'测试{test_id}')
        
        # 设置统一的时间轴范围
        ax.set_xlim(0, max_time)
        
        ax.set_title(col, fontsize=14, fontweight='bold')
        ax.set_xlabel('时间 (分钟)', fontsize=11)
        ax.set_ylabel('温度 (°C)', fontsize=11)
        ax.legend(fontsize=10, loc='best')
        ax.grid(True, alpha=0.3)
        
        # 添加统计信息
        stats_text = ""
        for test_id, df in data.items():
            if col in df.columns:
                avg_val = df[col].mean()
                max_val = df[col].max()
                min_val = df[col].min()
                stats_text += f"测试{test_id}: 均{avg_val:.1f}°C\n"
        
        ax.text(0.02, 0.98, stats_text.strip(), 
               transform=ax.transAxes, fontsize=9,
               bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8),
               verticalalignment='top')
    
    plt.tight_layout()
    plt.subplots_adjust(top=0.94)
    plt.savefig('统一时间轴完整对比图.png', dpi=300, bbox_inches='tight')
    print("✓ 统一时间轴完整对比图已保存: 统一时间轴完整对比图.png")
    plt.close()
    
    # 2. 创建关键参数统一时间轴对比图 (2x2)
    print("正在生成关键参数统一时间轴对比图...")
    
    key_params = ['环境温度', '遮阳罩表面温度', '制冷帐篷表面温度', '皮革表面温度']
    
    fig, axes = plt.subplots(2, 2, figsize=(20, 14))
    fig.suptitle('关键温度参数统一时间轴对比', fontsize=18, fontweight='bold')
    
    for i, param in enumerate(key_params):
        row = i // 2
        col = i % 2
        ax = axes[row, col]
        
        # 绘制每个测试的折线
        for test_id, df in data.items():
            if param in df.columns:
                time_minutes = df['时间'] / 60
                ax.plot(time_minutes, df[param], 
                       color=colors[test_id], 
                       linestyle=line_styles[test_id],
                       linewidth=3, alpha=0.9, 
                       label=f'测试{test_id}', 
                       marker='o', markersize=1.5, markevery=50)
        
        # 设置统一的时间轴范围
        ax.set_xlim(0, max_time)
        
        ax.set_title(param, fontsize=16, fontweight='bold')
        ax.set_xlabel('时间 (分钟)', fontsize=13)
        ax.set_ylabel('温度 (°C)', fontsize=13)
        ax.legend(fontsize=12, loc='best')
        ax.grid(True, alpha=0.4)
        
        # 添加详细统计信息
        stats_text = ""
        for test_id, df in data.items():
            if param in df.columns:
                start_val = df[param].iloc[0]
                end_val = df[param].iloc[-1]
                avg_val = df[param].mean()
                change = end_val - start_val
                stats_text += f'测试{test_id}: 起{start_val:.1f}°C→终{end_val:.1f}°C (均{avg_val:.1f}°C)\n'
        
        ax.text(0.02, 0.98, stats_text.strip(), transform=ax.transAxes, fontsize=10,
               bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.9),
               verticalalignment='top')
    
    plt.tight_layout()
    plt.subplots_adjust(top=0.94)
    plt.savefig('关键参数统一时间轴对比图.png', dpi=300, bbox_inches='tight')
    print("✓ 关键参数统一时间轴对比图已保存: 关键参数统一时间轴对比图.png")
    plt.close()
    
    # 3. 创建制冷效果统一时间轴专项对比图
    print("正在生成制冷效果统一时间轴专项对比图...")
    
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(24, 10))
    fig.suptitle('制冷效果统一时间轴专项对比分析', fontsize=18, fontweight='bold')
    
    # 左图：制冷帐篷温度对比
    for test_id, df in data.items():
        if '制冷帐篷表面温度' in df.columns:
            time_minutes = df['时间'] / 60
            ax1.plot(time_minutes, df['制冷帐篷表面温度'], 
                    color=colors[test_id], 
                    linestyle=line_styles[test_id],
                    linewidth=4, label=f'测试{test_id}-表面', alpha=0.9)
            if '制冷帐篷背面温度' in df.columns:
                ax1.plot(time_minutes, df['制冷帐篷背面温度'], 
                        color=colors[test_id], 
                        linestyle=line_styles[test_id],
                        linewidth=2, alpha=0.6, 
                        label=f'测试{test_id}-背面')
    
    ax1.set_xlim(0, max_time)
    ax1.set_title('制冷帐篷温度统一时间轴对比', fontsize=16, fontweight='bold')
    ax1.set_xlabel('时间 (分钟)', fontsize=13)
    ax1.set_ylabel('温度 (°C)', fontsize=13)
    ax1.legend(fontsize=11, loc='best')
    ax1.grid(True, alpha=0.3)
    
    # 右图：皮革温度对比
    for test_id, df in data.items():
        if '皮革表面温度' in df.columns:
            time_minutes = df['时间'] / 60
            ax2.plot(time_minutes, df['皮革表面温度'], 
                    color=colors[test_id], 
                    linestyle=line_styles[test_id],
                    linewidth=4, label=f'测试{test_id}-表面', alpha=0.9)
            if '皮革背面温度' in df.columns:
                ax2.plot(time_minutes, df['皮革背面温度'], 
                        color=colors[test_id], 
                        linestyle=line_styles[test_id],
                        linewidth=2, alpha=0.6, 
                        label=f'测试{test_id}-背面')
    
    ax2.set_xlim(0, max_time)
    ax2.set_title('皮革温度统一时间轴对比', fontsize=16, fontweight='bold')
    ax2.set_xlabel('时间 (分钟)', fontsize=13)
    ax2.set_ylabel('温度 (°C)', fontsize=13)
    ax2.legend(fontsize=11, loc='best')
    ax2.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.subplots_adjust(top=0.92)
    plt.savefig('制冷效果统一时间轴对比图.png', dpi=300, bbox_inches='tight')
    print("✓ 制冷效果统一时间轴对比图已保存: 制冷效果统一时间轴对比图.png")
    plt.close()
    
    # 4. 创建单一图表显示所有关键参数
    print("正在生成综合统一时间轴对比图...")
    
    fig, ax = plt.subplots(1, 1, figsize=(20, 12))
    fig.suptitle('综合统一时间轴对比 - 制冷帐篷表面温度', fontsize=20, fontweight='bold')
    
    # 只显示制冷帐篷表面温度进行清晰对比
    for test_id, df in data.items():
        if '制冷帐篷表面温度' in df.columns:
            time_minutes = df['时间'] / 60
            ax.plot(time_minutes, df['制冷帐篷表面温度'], 
                   color=colors[test_id], 
                   linestyle=line_styles[test_id],
                   linewidth=4, alpha=0.9, 
                   label=f'测试{test_id}',
                   marker='o', markersize=2, markevery=100)
    
    ax.set_xlim(0, max_time)
    ax.set_title('制冷帐篷表面温度 - 四测试统一时间轴对比', fontsize=18, fontweight='bold')
    ax.set_xlabel('时间 (分钟)', fontsize=15)
    ax.set_ylabel('温度 (°C)', fontsize=15)
    ax.legend(fontsize=14, loc='best')
    ax.grid(True, alpha=0.4)
    
    # 添加性能排名信息
    ranking_text = "制冷效果排名:\n"
    cooling_temps = {}
    for test_id, df in data.items():
        if '制冷帐篷表面温度' in df.columns:
            avg_temp = df['制冷帐篷表面温度'].mean()
            cooling_temps[test_id] = avg_temp
    
    sorted_tests = sorted(cooling_temps.items(), key=lambda x: x[1])
    for i, (test_id, temp) in enumerate(sorted_tests, 1):
        ranking_text += f"{i}. 测试{test_id}: {temp:.2f}°C\n"
    
    ax.text(0.02, 0.98, ranking_text.strip(), transform=ax.transAxes, fontsize=12,
           bbox=dict(boxstyle='round', facecolor='lightgreen', alpha=0.9),
           verticalalignment='top')
    
    plt.tight_layout()
    plt.savefig('综合统一时间轴对比图.png', dpi=300, bbox_inches='tight')
    print("✓ 综合统一时间轴对比图已保存: 综合统一时间轴对比图.png")
    plt.close()
    
    return True

def generate_unified_timeline_report(data, max_time):
    """生成统一时间轴分析报告"""
    print("\n正在生成统一时间轴分析报告...")
    
    report = []
    report.append("=" * 70)
    report.append("统一时间轴对比分析报告")
    report.append("=" * 70)
    report.append("")
    
    # 时间轴信息
    report.append("⏰ 统一时间轴信息:")
    report.append(f"   时间轴范围: 0 - {max_time:.1f} 分钟")
    report.append("")
    
    # 各测试的时间覆盖情况
    report.append("📊 各测试时间覆盖情况:")
    for test_id, df in data.items():
        test_max_time = df['时间'].max() / 60
        coverage = (test_max_time / max_time) * 100
        report.append(f"   测试{test_id}: 0 - {test_max_time:.1f} 分钟 (覆盖率: {coverage:.1f}%)")
    report.append("")
    
    # 制冷效果在统一时间轴上的表现
    report.append("❄️ 制冷效果统一时间轴分析:")
    cooling_param = '制冷帐篷表面温度'
    if all(cooling_param in df.columns for df in data.values()):
        cooling_performance = {}
        for test_id, df in data.items():
            avg_temp = df[cooling_param].mean()
            start_temp = df[cooling_param].iloc[0]
            end_temp = df[cooling_param].iloc[-1]
            cooling_performance[test_id] = {
                'avg': avg_temp,
                'start': start_temp,
                'end': end_temp,
                'change': end_temp - start_temp
            }
        
        # 排序
        sorted_tests = sorted(cooling_performance.items(), key=lambda x: x[1]['avg'])
        report.append("   制冷效果排名 (平均温度从低到高):")
        for i, (test_id, perf) in enumerate(sorted_tests, 1):
            report.append(f"     {i}. 测试{test_id}: 平均{perf['avg']:.2f}°C, "
                         f"起始{perf['start']:.1f}°C→结束{perf['end']:.1f}°C "
                         f"(变化{perf['change']:+.1f}°C)")
    
    report.append("")
    report.append("📈 生成的统一时间轴图表文件:")
    report.append("   1. 统一时间轴完整对比图.png - 9个参数在统一时间轴上的完整对比")
    report.append("   2. 关键参数统一时间轴对比图.png - 4个关键参数统一时间轴对比")
    report.append("   3. 制冷效果统一时间轴对比图.png - 制冷效果统一时间轴专项对比")
    report.append("   4. 综合统一时间轴对比图.png - 制冷帐篷表面温度综合对比")
    report.append("")
    report.append("💡 统一时间轴的优势:")
    report.append("   • 可以直观看到各测试在相同时间点的表现")
    report.append("   • 便于比较不同测试的时间响应特性")
    report.append("   • 清晰展示各测试的数据覆盖范围")
    report.append("   • 更准确地评估长期稳定性")
    report.append("")
    report.append("=" * 70)
    
    # 保存报告
    with open('统一时间轴对比分析报告.txt', 'w', encoding='utf-8') as f:
        f.write('\n'.join(report))
    
    # 打印报告
    for line in report:
        print(line)
    
    print("✓ 统一时间轴对比分析报告已保存: 统一时间轴对比分析报告.txt")

def main():
    """主函数"""
    print("统一时间轴对比分析工具")
    print("=" * 60)
    
    # 加载数据
    data, max_time = load_and_prepare_data()
    
    if len(data) < 2:
        print("错误：至少需要2个数据文件才能进行对比")
        return False
    
    print(f"\n成功加载 {len(data)} 个测试数据文件")
    
    # 创建统一时间轴对比图表
    if create_unified_timeline_charts(data, max_time):
        print("\n✓ 所有统一时间轴对比图表生成完成！")
        
        # 生成分析报告
        generate_unified_timeline_report(data, max_time)
        
        print("\n🎉 统一时间轴对比分析完成！")
        print("请查看生成的PNG图片文件，所有测试数据现在都在同一个时间轴上进行对比。")
        return True
    else:
        print("\n✗ 图表生成失败")
        return False

if __name__ == "__main__":
    success = main()
    if not success:
        print("\n程序执行失败，请检查数据文件。")
