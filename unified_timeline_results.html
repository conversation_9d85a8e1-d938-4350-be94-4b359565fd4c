<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>统一时间轴对比分析结果</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            line-height: 1.6;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
            font-size: 28px;
        }
        h2 {
            color: #34495e;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
            margin-top: 40px;
        }
        .summary {
            background-color: #e8f4fd;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
            border-left: 4px solid #3498db;
        }
        .chart-section {
            margin-bottom: 40px;
        }
        .chart-container {
            text-align: center;
            margin-bottom: 30px;
        }
        .chart-container img {
            max-width: 100%;
            height: auto;
            border: 1px solid #ddd;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .chart-title {
            font-size: 18px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 15px;
        }
        .chart-description {
            color: #7f8c8d;
            font-size: 14px;
            margin-top: 10px;
            max-width: 900px;
            margin-left: auto;
            margin-right: auto;
        }
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        .data-table th, .data-table td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: center;
        }
        .data-table th {
            background-color: #f8f9fa;
            font-weight: bold;
            color: #2c3e50;
        }
        .data-table tr:nth-child(even) {
            background-color: #f8f9fa;
        }
        .highlight {
            background-color: #d4edda;
            font-weight: bold;
        }
        .note {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 15px;
            border-radius: 5px;
            margin-top: 20px;
        }
        .timeline-info {
            background-color: #e9f7ef;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
            border-left: 4px solid #27ae60;
        }
        .ranking {
            background-color: #fdf2e9;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
            border-left: 4px solid #e67e22;
        }
        .ranking ol {
            font-size: 16px;
            line-height: 1.8;
        }
        .ranking li {
            margin-bottom: 8px;
        }
        .best {
            color: #27ae60;
            font-weight: bold;
        }
        .legend {
            display: flex;
            justify-content: center;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        .legend-item {
            display: flex;
            align-items: center;
            margin: 0 15px 10px 15px;
            padding: 8px 12px;
            background-color: #f8f9fa;
            border-radius: 5px;
        }
        .color-box {
            width: 20px;
            height: 20px;
            margin-right: 8px;
            border-radius: 3px;
            border: 1px solid #ddd;
        }
        .line-style {
            width: 30px;
            height: 3px;
            margin-right: 8px;
            border-radius: 2px;
        }
        .solid { background: linear-gradient(to right, transparent 0%, currentColor 0%); }
        .dashed { background: repeating-linear-gradient(to right, currentColor 0px, currentColor 5px, transparent 5px, transparent 10px); }
        .dotdash { background: repeating-linear-gradient(to right, currentColor 0px, currentColor 3px, transparent 3px, transparent 6px, currentColor 6px, currentColor 12px, transparent 12px, transparent 15px); }
        .dotted { background: repeating-linear-gradient(to right, currentColor 0px, currentColor 2px, transparent 2px, transparent 6px); }
    </style>
</head>
<body>
    <div class="container">
        <h1>统一时间轴对比分析结果</h1>
        
        <div class="summary">
            <h3>📊 分析概要</h3>
            <p>本次分析将所有四个测试的数据放在<strong>同一个时间轴</strong>上进行对比，时间轴范围为0-521.5分钟。</p>
            <p>这种对比方式可以直观看到各测试在相同时间点的表现，更准确地评估长期稳定性和时间响应特性。</p>
        </div>

        <div class="timeline-info">
            <h3>⏰ 统一时间轴信息</h3>
            <p><strong>时间轴范围：</strong>0 - 521.5 分钟（以测试12的最长时间为准）</p>
            
            <h4>各测试时间覆盖情况：</h4>
            <table class="data-table">
                <thead>
                    <tr>
                        <th>测试编号</th>
                        <th>时间范围</th>
                        <th>覆盖率</th>
                        <th>数据点数量</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>测试5</td>
                        <td>0 - 403.8 分钟</td>
                        <td>77.4%</td>
                        <td>2,424 个</td>
                    </tr>
                    <tr>
                        <td>测试11</td>
                        <td>0 - 229.2 分钟</td>
                        <td>43.9%</td>
                        <td>1,376 个</td>
                    </tr>
                    <tr class="highlight">
                        <td>测试12</td>
                        <td>0 - 521.5 分钟</td>
                        <td>100.0%</td>
                        <td>3,129 个</td>
                    </tr>
                    <tr>
                        <td>测试13</td>
                        <td>0 - 475.2 分钟</td>
                        <td>91.1%</td>
                        <td>2,852 个</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="ranking">
            <h3>🏆 制冷效果统一时间轴排名</h3>
            <p>基于制冷帐篷表面温度的平均值排名：</p>
            <ol>
                <li class="best">测试12：平均37.49°C，起始37.2°C→结束32.9°C (变化-4.3°C) 🥇</li>
                <li>测试5：平均39.22°C，起始44.8°C→结束32.1°C (变化-12.7°C) 🥈</li>
                <li>测试11：平均44.77°C，起始44.1°C→结束47.0°C (变化+3.0°C) 🥉</li>
                <li>测试13：平均45.19°C，起始33.0°C→结束35.9°C (变化+3.0°C)</li>
            </ol>
            
            <div class="note">
                <strong>💡 关键发现：</strong>
                <ul>
                    <li><strong>测试12</strong>不仅制冷效果最佳，而且在整个测试期间保持稳定的低温</li>
                    <li><strong>测试5</strong>显示出最大的温度下降（-12.7°C），表明良好的制冷响应</li>
                    <li><strong>测试11和13</strong>在测试过程中温度略有上升，制冷效果相对较差</li>
                </ul>
            </div>
        </div>

        <div class="legend">
            <div class="legend-item">
                <div class="color-box" style="background-color: #4ECDC4;"></div>
                <div class="line-style solid" style="color: #4ECDC4;"></div>
                <span>测试5 (实线)</span>
            </div>
            <div class="legend-item">
                <div class="color-box" style="background-color: #FF6B6B;"></div>
                <div class="line-style dashed" style="color: #FF6B6B;"></div>
                <span>测试11 (虚线)</span>
            </div>
            <div class="legend-item">
                <div class="color-box" style="background-color: #45B7D1;"></div>
                <div class="line-style dotdash" style="color: #45B7D1;"></div>
                <span>测试12 (点划线)</span>
            </div>
            <div class="legend-item">
                <div class="color-box" style="background-color: #FFA07A;"></div>
                <div class="line-style dotted" style="color: #FFA07A;"></div>
                <span>测试13 (点线)</span>
            </div>
        </div>

        <h2>📊 统一时间轴对比图表</h2>
        
        <div class="chart-section">
            <div class="chart-container">
                <div class="chart-title">综合统一时间轴对比图 - 制冷帐篷表面温度</div>
                <img src="综合统一时间轴对比图.png" alt="综合统一时间轴对比图">
                <div class="chart-description">
                    这是最重要的对比图，清晰展示了四个测试在同一时间轴上的制冷帐篷表面温度变化。
                    可以直观看到测试12在整个时间段内都保持最低温度，而测试5显示出最大的温度下降趋势。
                </div>
            </div>
        </div>

        <div class="chart-section">
            <div class="chart-container">
                <div class="chart-title">统一时间轴完整对比图</div>
                <img src="统一时间轴完整对比图.png" alt="统一时间轴完整对比图">
                <div class="chart-description">
                    展示了所有9个温度参数在统一时间轴上的完整对比，每个子图都使用相同的时间范围(0-521.5分钟)，
                    便于横向比较各参数在不同测试中的表现。
                </div>
            </div>
        </div>

        <div class="chart-section">
            <div class="chart-container">
                <div class="chart-title">关键参数统一时间轴对比图</div>
                <img src="关键参数统一时间轴对比图.png" alt="关键参数统一时间轴对比图">
                <div class="chart-description">
                    重点展示环境温度、遮阳罩表面温度、制冷帐篷表面温度、皮革表面温度等4个关键参数
                    在统一时间轴上的对比，包含详细的统计信息。
                </div>
            </div>
        </div>

        <div class="chart-section">
            <div class="chart-container">
                <div class="chart-title">制冷效果统一时间轴专项对比图</div>
                <img src="制冷效果统一时间轴对比图.png" alt="制冷效果统一时间轴对比图">
                <div class="chart-description">
                    专门对比制冷帐篷和皮革的表面与背面温度在统一时间轴上的变化，
                    突出展示各测试的制冷效果差异和时间响应特性。
                </div>
            </div>
        </div>

        <h2>💡 统一时间轴分析的优势</h2>
        <div class="summary">
            <ul>
                <li><strong>直观对比：</strong>可以直观看到各测试在相同时间点的表现</li>
                <li><strong>时间响应：</strong>便于比较不同测试的时间响应特性</li>
                <li><strong>数据覆盖：</strong>清晰展示各测试的数据覆盖范围</li>
                <li><strong>长期稳定性：</strong>更准确地评估长期稳定性</li>
                <li><strong>趋势分析：</strong>能够识别温度变化的趋势和拐点</li>
            </ul>
        </div>

        <h2>🎯 结论与建议</h2>
        <div class="ranking">
            <h4>基于统一时间轴分析的推荐：</h4>
            <p><strong>首选方案：测试12</strong></p>
            <ul>
                <li>制冷效果最佳且稳定</li>
                <li>测试时间最长，数据最充分</li>
                <li>在整个时间段内都保持优异表现</li>
            </ul>
            
            <p><strong>备选方案：测试5</strong></p>
            <ul>
                <li>显示出良好的制冷响应能力</li>
                <li>温度下降幅度最大</li>
                <li>在前400分钟内表现稳定</li>
            </ul>
        </div>

        <h2>📁 生成的文件</h2>
        <ul>
            <li><strong>统一时间轴完整对比图.png</strong> - 9个参数在统一时间轴上的完整对比</li>
            <li><strong>关键参数统一时间轴对比图.png</strong> - 4个关键参数统一时间轴对比</li>
            <li><strong>制冷效果统一时间轴对比图.png</strong> - 制冷效果统一时间轴专项对比</li>
            <li><strong>综合统一时间轴对比图.png</strong> - 制冷帐篷表面温度综合对比</li>
            <li><strong>统一时间轴对比分析报告.txt</strong> - 详细分析报告</li>
        </ul>
    </div>
</body>
</html>
