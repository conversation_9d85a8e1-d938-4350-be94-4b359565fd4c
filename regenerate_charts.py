#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
重新生成修复异常点后的统一时间轴对比图
"""

import pandas as pd
import matplotlib.pyplot as plt
import matplotlib
import numpy as np
import os
import warnings
warnings.filterwarnings('ignore')

# 设置matplotlib后端和中文字体
matplotlib.use('Agg')
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def load_data():
    """加载修复后的数据"""
    print("正在加载修复后的数据...")
    
    files = {
        '5': '5_fixed_headers.csv',
        '11': '11_fixed_headers.csv',
        '12': '12_fixed_headers.csv',
        '13': '13_fixed_headers.csv'
    }
    
    data = {}
    max_time = 0
    
    for test_id, filename in files.items():
        if os.path.exists(filename):
            try:
                df = pd.read_csv(filename)
                data[test_id] = df
                test_max_time = df['时间'].max() / 60
                max_time = max(max_time, test_max_time)
                print(f"✓ 测试{test_id}: {len(df)} 个数据点, 最大时间: {test_max_time:.1f} 分钟")
            except Exception as e:
                print(f"✗ 测试{test_id}: 加载失败 - {e}")
    
    return data, max_time

def create_fixed_comparison_chart(data, max_time):
    """创建修复后的综合对比图"""
    print("正在生成修复后的综合统一时间轴对比图...")
    
    # 颜色配置
    colors = {
        '5': '#4ECDC4',   # 青绿色
        '11': '#FF6B6B',  # 红色
        '12': '#45B7D1',  # 蓝色
        '13': '#FFA07A'   # 橙色
    }
    
    # 线型配置
    line_styles = {
        '5': '-',      # 实线
        '11': '--',    # 虚线
        '12': '-.',    # 点划线
        '13': ':'      # 点线
    }
    
    # 创建图表
    fig, ax = plt.subplots(1, 1, figsize=(20, 12))
    fig.suptitle('修复异常点后 - 制冷帐篷表面温度统一时间轴对比', fontsize=20, fontweight='bold')
    
    # 绘制制冷帐篷表面温度对比
    for test_id, df in data.items():
        if '制冷帐篷表面温度' in df.columns:
            time_minutes = df['时间'] / 60
            ax.plot(time_minutes, df['制冷帐篷表面温度'], 
                   color=colors[test_id], 
                   linestyle=line_styles[test_id],
                   linewidth=4, alpha=0.9, 
                   label=f'测试{test_id}',
                   marker='o', markersize=2, markevery=100)
    
    ax.set_xlim(0, max_time)
    ax.set_title('制冷帐篷表面温度 - 四测试统一时间轴对比 (已修复异常点)', fontsize=18, fontweight='bold')
    ax.set_xlabel('时间 (分钟)', fontsize=15)
    ax.set_ylabel('温度 (°C)', fontsize=15)
    ax.legend(fontsize=14, loc='best')
    ax.grid(True, alpha=0.4)
    
    # 添加性能排名信息
    ranking_text = "制冷效果排名 (修复后):\n"
    cooling_temps = {}
    for test_id, df in data.items():
        if '制冷帐篷表面温度' in df.columns:
            avg_temp = df['制冷帐篷表面温度'].mean()
            cooling_temps[test_id] = avg_temp
    
    sorted_tests = sorted(cooling_temps.items(), key=lambda x: x[1])
    for i, (test_id, temp) in enumerate(sorted_tests, 1):
        ranking_text += f"{i}. 测试{test_id}: {temp:.2f}°C\n"
    
    ax.text(0.02, 0.98, ranking_text.strip(), transform=ax.transAxes, fontsize=12,
           bbox=dict(boxstyle='round', facecolor='lightgreen', alpha=0.9),
           verticalalignment='top')
    
    plt.tight_layout()
    plt.savefig('修复异常点后_综合统一时间轴对比图.png', dpi=300, bbox_inches='tight')
    print("✓ 修复后对比图已保存: 修复异常点后_综合统一时间轴对比图.png")
    plt.close()
    
    return cooling_temps

def create_anomaly_comparison(data, max_time):
    """创建异常点修复前后对比图"""
    print("正在生成遮阳罩皮革表面温度修复对比图...")
    
    colors = {
        '5': '#4ECDC4',   # 青绿色
        '11': '#FF6B6B',  # 红色
        '12': '#45B7D1',  # 蓝色
        '13': '#FFA07A'   # 橙色
    }
    
    fig, ax = plt.subplots(1, 1, figsize=(20, 10))
    fig.suptitle('遮阳罩皮革表面温度 - 修复异常点后的统一时间轴对比', fontsize=18, fontweight='bold')
    
    # 绘制遮阳罩皮革表面温度对比
    for test_id, df in data.items():
        if '遮阳罩皮革表面温度' in df.columns:
            time_minutes = df['时间'] / 60
            ax.plot(time_minutes, df['遮阳罩皮革表面温度'], 
                   color=colors[test_id], 
                   linewidth=3, alpha=0.8, 
                   label=f'测试{test_id}',
                   marker='o', markersize=1.5, markevery=50)
    
    ax.set_xlim(0, max_time)
    ax.set_ylim(30, 45)  # 设置合理的温度范围
    ax.set_title('遮阳罩皮革表面温度 - 异常点已修复', fontsize=16, fontweight='bold')
    ax.set_xlabel('时间 (分钟)', fontsize=13)
    ax.set_ylabel('温度 (°C)', fontsize=13)
    ax.legend(fontsize=12, loc='best')
    ax.grid(True, alpha=0.4)
    
    # 添加修复说明
    fix_text = "异常点修复说明:\n"
    fix_text += "• 测试13在0分钟: 2252°C → 38.5°C\n"
    fix_text += "• 测试13在385分钟: 295-309°C → 37.1-37.9°C\n"
    fix_text += "• 使用合理插值替换异常值"
    
    ax.text(0.98, 0.98, fix_text, transform=ax.transAxes, fontsize=11,
           bbox=dict(boxstyle='round', facecolor='yellow', alpha=0.8),
           verticalalignment='top', horizontalalignment='right')
    
    plt.tight_layout()
    plt.savefig('修复异常点后_遮阳罩皮革表面温度对比图.png', dpi=300, bbox_inches='tight')
    print("✓ 遮阳罩皮革表面温度修复对比图已保存: 修复异常点后_遮阳罩皮革表面温度对比图.png")
    plt.close()

def generate_fixed_report(data, max_time, cooling_temps):
    """生成修复后的分析报告"""
    print("\n正在生成修复后的分析报告...")
    
    report = []
    report.append("=" * 70)
    report.append("修复异常点后 - 统一时间轴对比分析报告")
    report.append("=" * 70)
    report.append("")
    
    # 异常点修复说明
    report.append("🔧 异常点修复说明:")
    report.append("   测试13数据中发现以下异常点并已修复:")
    report.append("   • 第1行 (0分钟): 遮阳罩表面温度 2252.35°C → 38.5°C")
    report.append("   • 385分钟附近: 遮阳罩皮革表面温度 221-309°C → 37.1-37.9°C")
    report.append("   • 修复方法: 使用合理的插值替换异常值")
    report.append("")
    
    # 时间轴信息
    report.append("⏰ 统一时间轴信息:")
    report.append(f"   时间轴范围: 0 - {max_time:.1f} 分钟")
    report.append("")
    
    # 各测试的时间覆盖情况
    report.append("📊 各测试时间覆盖情况:")
    for test_id, df in data.items():
        test_max_time = df['时间'].max() / 60
        coverage = (test_max_time / max_time) * 100
        report.append(f"   测试{test_id}: 0 - {test_max_time:.1f} 分钟 (覆盖率: {coverage:.1f}%)")
    report.append("")
    
    # 制冷效果分析
    report.append("❄️ 制冷效果分析 (修复后):")
    sorted_tests = sorted(cooling_temps.items(), key=lambda x: x[1])
    report.append("   制冷效果排名 (平均温度从低到高):")
    for i, (test_id, temp) in enumerate(sorted_tests, 1):
        df = data[test_id]
        start_temp = df['制冷帐篷表面温度'].iloc[0]
        end_temp = df['制冷帐篷表面温度'].iloc[-1]
        change = end_temp - start_temp
        report.append(f"     {i}. 测试{test_id}: 平均{temp:.2f}°C, "
                     f"起始{start_temp:.1f}°C→结束{end_temp:.1f}°C "
                     f"(变化{change:+.1f}°C)")
    
    report.append("")
    report.append("📈 生成的修复后图表文件:")
    report.append("   1. 修复异常点后_综合统一时间轴对比图.png - 制冷帐篷表面温度对比")
    report.append("   2. 修复异常点后_遮阳罩皮革表面温度对比图.png - 遮阳罩皮革表面温度对比")
    report.append("")
    report.append("💡 修复后的数据更加可靠，分析结果更准确。")
    report.append("")
    report.append("=" * 70)
    
    # 保存报告
    with open('修复异常点后_对比分析报告.txt', 'w', encoding='utf-8') as f:
        f.write('\n'.join(report))
    
    # 打印报告
    for line in report:
        print(line)
    
    print("✓ 修复后对比分析报告已保存: 修复异常点后_对比分析报告.txt")

def main():
    """主函数"""
    print("重新生成修复异常点后的统一时间轴对比图")
    print("=" * 60)
    
    # 加载修复后的数据
    data, max_time = load_data()
    
    if len(data) < 2:
        print("错误：至少需要2个数据文件才能进行对比")
        return False
    
    print(f"\n成功加载 {len(data)} 个测试数据文件")
    print(f"统一时间轴范围: 0 - {max_time:.1f} 分钟")
    
    # 创建修复后的对比图表
    cooling_temps = create_fixed_comparison_chart(data, max_time)
    create_anomaly_comparison(data, max_time)
    
    # 生成修复后的分析报告
    generate_fixed_report(data, max_time, cooling_temps)
    
    print("\n🎉 修复异常点后的对比分析完成！")
    print("异常点已修复，数据更加可靠，分析结果更准确。")
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        print("\n程序执行失败，请检查数据文件。")
