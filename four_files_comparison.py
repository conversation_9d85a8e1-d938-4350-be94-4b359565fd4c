#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
四文件对比分析工具
对比测试5、11、12、13的数据，生成折线图
"""

import pandas as pd
import matplotlib.pyplot as plt
import matplotlib
import numpy as np
import os
import warnings
warnings.filterwarnings('ignore')

# 设置matplotlib后端和中文字体
matplotlib.use('Agg')  # 使用非交互式后端
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def load_data():
    """加载所有测试数据"""
    data = {}
    
    # 文件映射
    files = {
        '5': '5_fixed_headers.csv',
        '11': '11_fixed_headers.csv',
        '12': '12_fixed_headers.csv',
        '13': '13_fixed_headers.csv'
    }
    
    print("正在加载数据文件...")
    
    for test_id, filename in files.items():
        if os.path.exists(filename):
            try:
                if filename.endswith('.csv'):
                    df = pd.read_csv(filename)
                else:
                    # 尝试不同的Excel读取方法
                    try:
                        df = pd.read_excel(filename, engine='openpyxl')
                    except:
                        try:
                            df = pd.read_excel(filename, engine='xlrd')
                        except:
                            # 如果Excel读取失败，尝试转换为CSV
                            print(f"   正在尝试转换 {filename} 为CSV格式...")
                            temp_df = pd.read_excel(filename, engine=None)
                            csv_filename = filename.replace('.xlsx', '_converted.csv')
                            temp_df.to_csv(csv_filename, index=False, encoding='utf-8-sig')
                            df = pd.read_csv(csv_filename)
                            print(f"   已转换为: {csv_filename}")

                data[test_id] = df
                print(f"✓ 测试{test_id}: {len(df)} 个数据点 ({filename})")

            except Exception as e:
                print(f"✗ 测试{test_id}: 加载失败 - {e}")
                # 尝试手动转换Excel文件
                if filename.endswith('.xlsx'):
                    print(f"   尝试手动处理Excel文件: {filename}")
                    try:
                        # 使用xlwings或其他方法
                        import xlwings as xw
                        app = xw.App(visible=False)
                        wb = app.books.open(filename)
                        ws = wb.sheets[0]
                        data_range = ws.used_range
                        df = pd.DataFrame(data_range.value[1:], columns=data_range.value[0])
                        wb.close()
                        app.quit()

                        data[test_id] = df
                        print(f"✓ 测试{test_id}: {len(df)} 个数据点 (手动转换 {filename})")
                    except:
                        print(f"   手动转换也失败，跳过文件: {filename}")
        else:
            print(f"✗ 测试{test_id}: 文件不存在 - {filename}")
    
    return data

def create_comparison_charts(data):
    """创建对比图表"""
    if len(data) < 2:
        print("错误：至少需要2个数据文件才能进行对比")
        return False
    
    # 温度参数列表
    temp_columns = [
        '遮阳罩表面温度', '遮阳罩背面温度', '遮阳罩皮革表面温度',
        '制冷帐篷表面温度', '制冷帐篷背面温度', '制冷帐篷皮革温度',
        '皮革表面温度', '皮革背面温度', '环境温度'
    ]
    
    # 颜色配置
    colors = {
        '5': '#4ECDC4',   # 青绿色
        '11': '#FF6B6B',  # 红色
        '12': '#45B7D1',  # 蓝色
        '13': '#FFA07A'   # 橙色
    }
    
    # 1. 创建完整对比图 (3x3)
    print("正在生成完整对比图...")
    
    fig, axes = plt.subplots(3, 3, figsize=(20, 16))
    fig.suptitle('测试5、11、12、13 - 完整温度对比折线图', fontsize=18, fontweight='bold')
    
    for i, col in enumerate(temp_columns):
        row = i // 3
        col_idx = i % 3
        ax = axes[row, col_idx]
        
        # 绘制每个测试的折线
        for test_id, df in data.items():
            if col in df.columns:
                time_minutes = df['时间'] / 60  # 转换为分钟
                ax.plot(time_minutes, df[col], 
                       color=colors[test_id], linewidth=2, alpha=0.8, 
                       label=f'测试{test_id}')
        
        ax.set_title(col, fontsize=12, fontweight='bold')
        ax.set_xlabel('时间 (分钟)', fontsize=10)
        ax.set_ylabel('温度 (°C)', fontsize=10)
        ax.legend(fontsize=9)
        ax.grid(True, alpha=0.3)
        
        # 添加平均值信息
        avg_text = ""
        for test_id, df in data.items():
            if col in df.columns:
                avg_val = df[col].mean()
                avg_text += f"测试{test_id}: {avg_val:.1f}°C\n"
        
        ax.text(0.02, 0.98, avg_text.strip(), 
               transform=ax.transAxes, fontsize=8,
               bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.7),
               verticalalignment='top')
    
    plt.tight_layout()
    plt.subplots_adjust(top=0.93)
    plt.savefig('四文件完整对比图.png', dpi=300, bbox_inches='tight')
    print("✓ 完整对比图已保存: 四文件完整对比图.png")
    plt.close()
    
    # 2. 创建关键参数对比图 (2x2)
    print("正在生成关键参数对比图...")
    
    key_params = ['环境温度', '遮阳罩表面温度', '制冷帐篷表面温度', '皮革表面温度']
    
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    fig.suptitle('关键温度参数对比 - 测试5、11、12、13', fontsize=16, fontweight='bold')
    
    for i, param in enumerate(key_params):
        row = i // 2
        col = i % 2
        ax = axes[row, col]
        
        # 绘制每个测试的折线
        for test_id, df in data.items():
            if param in df.columns:
                time_minutes = df['时间'] / 60
                ax.plot(time_minutes, df[param], 
                       color=colors[test_id], linewidth=3, alpha=0.9, 
                       label=f'测试{test_id}', marker='o', markersize=1)
        
        ax.set_title(param, fontsize=14, fontweight='bold')
        ax.set_xlabel('时间 (分钟)', fontsize=12)
        ax.set_ylabel('温度 (°C)', fontsize=12)
        ax.legend(fontsize=11)
        ax.grid(True, alpha=0.4)
        
        # 添加统计信息
        stats_text = ""
        for test_id, df in data.items():
            if param in df.columns:
                start_val = df[param].iloc[0]
                end_val = df[param].iloc[-1]
                change = end_val - start_val
                stats_text += f'测试{test_id}: {start_val:.1f}→{end_val:.1f}°C ({change:+.1f})\n'
        
        ax.text(0.02, 0.98, stats_text.strip(), transform=ax.transAxes, fontsize=9,
               bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8),
               verticalalignment='top')
    
    plt.tight_layout()
    plt.subplots_adjust(top=0.93)
    plt.savefig('四文件关键参数对比图.png', dpi=300, bbox_inches='tight')
    print("✓ 关键参数对比图已保存: 四文件关键参数对比图.png")
    plt.close()
    
    # 3. 创建制冷效果专项对比图
    print("正在生成制冷效果专项对比图...")
    
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(18, 8))
    fig.suptitle('制冷效果专项对比分析 - 测试5、11、12、13', fontsize=16, fontweight='bold')
    
    # 左图：制冷帐篷温度对比
    for test_id, df in data.items():
        if '制冷帐篷表面温度' in df.columns:
            time_minutes = df['时间'] / 60
            ax1.plot(time_minutes, df['制冷帐篷表面温度'], 
                    color=colors[test_id], linewidth=3, label=f'测试{test_id}-表面', alpha=0.9)
            if '制冷帐篷背面温度' in df.columns:
                ax1.plot(time_minutes, df['制冷帐篷背面温度'], 
                        color=colors[test_id], linewidth=2, linestyle='--', 
                        label=f'测试{test_id}-背面', alpha=0.7)
    
    ax1.set_title('制冷帐篷温度对比', fontsize=14, fontweight='bold')
    ax1.set_xlabel('时间 (分钟)', fontsize=12)
    ax1.set_ylabel('温度 (°C)', fontsize=12)
    ax1.legend(fontsize=10)
    ax1.grid(True, alpha=0.3)
    
    # 右图：皮革温度对比
    for test_id, df in data.items():
        if '皮革表面温度' in df.columns:
            time_minutes = df['时间'] / 60
            ax2.plot(time_minutes, df['皮革表面温度'], 
                    color=colors[test_id], linewidth=3, label=f'测试{test_id}-表面', alpha=0.9)
            if '皮革背面温度' in df.columns:
                ax2.plot(time_minutes, df['皮革背面温度'], 
                        color=colors[test_id], linewidth=2, linestyle='--', 
                        label=f'测试{test_id}-背面', alpha=0.7)
    
    ax2.set_title('皮革温度对比', fontsize=14, fontweight='bold')
    ax2.set_xlabel('时间 (分钟)', fontsize=12)
    ax2.set_ylabel('温度 (°C)', fontsize=12)
    ax2.legend(fontsize=10)
    ax2.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.subplots_adjust(top=0.9)
    plt.savefig('四文件制冷效果对比图.png', dpi=300, bbox_inches='tight')
    print("✓ 制冷效果对比图已保存: 四文件制冷效果对比图.png")
    plt.close()
    
    return True

def generate_summary_report(data):
    """生成对比分析报告"""
    print("\n正在生成对比分析报告...")
    
    report = []
    report.append("=" * 60)
    report.append("测试5、11、12、13 对比分析报告")
    report.append("=" * 60)
    report.append("")
    
    # 数据概览
    report.append("📊 数据概览:")
    for test_id, df in data.items():
        duration = df['时间'].max() / 60  # 分钟
        report.append(f"   测试{test_id}: {len(df):,} 个数据点, 持续时间 {duration:.1f} 分钟")
    report.append("")
    
    # 关键参数平均值对比
    key_params = ['环境温度', '制冷帐篷表面温度', '皮革表面温度']
    report.append("🌡️ 关键参数平均值对比:")
    
    for param in key_params:
        report.append(f"   {param}:")
        for test_id, df in data.items():
            if param in df.columns:
                avg_val = df[param].mean()
                report.append(f"     测试{test_id}: {avg_val:.2f}°C")
        report.append("")
    
    # 制冷效果分析
    report.append("❄️ 制冷效果分析:")
    cooling_param = '制冷帐篷表面温度'
    if all(cooling_param in df.columns for df in data.values()):
        cooling_avgs = {}
        for test_id, df in data.items():
            cooling_avgs[test_id] = df[cooling_param].mean()
        
        # 找出制冷效果最好的测试
        best_test = min(cooling_avgs, key=cooling_avgs.get)
        report.append(f"   制冷效果最佳: 测试{best_test} (平均温度: {cooling_avgs[best_test]:.2f}°C)")
        
        # 制冷效果排序
        sorted_tests = sorted(cooling_avgs.items(), key=lambda x: x[1])
        report.append("   制冷效果排序 (从好到差):")
        for i, (test_id, avg_temp) in enumerate(sorted_tests, 1):
            report.append(f"     {i}. 测试{test_id}: {avg_temp:.2f}°C")
    
    report.append("")
    report.append("📈 生成的图表文件:")
    report.append("   1. 四文件完整对比图.png - 9个参数完整对比")
    report.append("   2. 四文件关键参数对比图.png - 4个关键参数对比")
    report.append("   3. 四文件制冷效果对比图.png - 制冷效果专项对比")
    report.append("")
    report.append("=" * 60)
    
    # 保存报告
    with open('四文件对比分析报告.txt', 'w', encoding='utf-8') as f:
        f.write('\n'.join(report))
    
    # 打印报告
    for line in report:
        print(line)
    
    print("✓ 对比分析报告已保存: 四文件对比分析报告.txt")

def main():
    """主函数"""
    print("测试5、11、12、13 数据对比分析工具")
    print("=" * 50)
    
    # 加载数据
    data = load_data()
    
    if len(data) == 0:
        print("错误：没有找到任何可用的数据文件")
        return False
    
    print(f"\n成功加载 {len(data)} 个测试数据文件")
    
    # 创建对比图表
    if create_comparison_charts(data):
        print("\n✓ 所有对比图表生成完成！")
        
        # 生成分析报告
        generate_summary_report(data)
        
        print("\n🎉 四文件对比分析完成！")
        print("请查看生成的PNG图片文件和分析报告。")
        return True
    else:
        print("\n✗ 图表生成失败")
        return False

if __name__ == "__main__":
    success = main()
    if not success:
        print("\n程序执行失败，请检查数据文件和依赖包。")
