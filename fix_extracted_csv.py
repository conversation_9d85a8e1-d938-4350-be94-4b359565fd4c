#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复提取的CSV文件
添加正确的列标题并清理数据
"""

import pandas as pd
import os

def fix_csv_file(input_file, output_file):
    """修复CSV文件格式"""
    try:
        print(f"正在处理: {input_file}")
        
        # 读取原始CSV文件
        df = pd.read_csv(input_file, header=None)
        
        # 删除第一行（空行）
        df = df.drop(0).reset_index(drop=True)
        
        # 添加列标题（与5_fixed_headers.csv相同的格式）
        columns = [
            'Time', '时间', '遮阳罩表面温度', '遮阳罩背面温度', '遮阳罩皮革表面温度',
            '制冷帐篷表面温度', '制冷帐篷背面温度', '制冷帐篷皮革温度',
            '皮革表面温度', '皮革背面温度', '环境温度'
        ]
        
        # 确保列数匹配
        if len(df.columns) > len(columns):
            # 如果有多余的列，截取前面的列
            df = df.iloc[:, :len(columns)]
        elif len(df.columns) < len(columns):
            # 如果列数不够，添加空列
            for i in range(len(df.columns), len(columns)):
                df[i] = ''
        
        # 设置列名
        df.columns = columns
        
        # 删除完全为空的行
        df = df.dropna(how='all').reset_index(drop=True)
        
        # 保存修复后的文件
        df.to_csv(output_file, index=False, encoding='utf-8-sig')
        
        print(f"  成功修复: {output_file}")
        print(f"  数据行数: {len(df)}")
        print(f"  列数: {len(df.columns)}")
        
        # 显示数据预览
        print("  数据预览:")
        print(df.head(3).to_string(max_cols=6))
        print()
        
        return True
        
    except Exception as e:
        print(f"  修复失败: {e}")
        return False

def main():
    """主函数"""
    print("CSV文件修复工具")
    print("=" * 50)
    
    # 要修复的文件
    files_to_fix = [
        ('12_extracted.csv', '12_fixed_headers.csv'),
        ('13_extracted.csv', '13_fixed_headers.csv')
    ]
    
    success_count = 0
    
    for input_file, output_file in files_to_fix:
        if os.path.exists(input_file):
            if fix_csv_file(input_file, output_file):
                success_count += 1
        else:
            print(f"文件不存在: {input_file}")
    
    print("=" * 50)
    print(f"修复结果: {success_count}/{len(files_to_fix)} 个文件成功")
    
    if success_count > 0:
        print("\n现在可以运行完整的四文件对比分析了！")
        
        # 检查所有四个文件是否都存在
        all_files = [
            '5_fixed_headers.csv',
            '11_fixed_headers.csv', 
            '12_fixed_headers.csv',
            '13_fixed_headers.csv'
        ]
        
        existing_files = [f for f in all_files if os.path.exists(f)]
        print(f"可用的数据文件: {len(existing_files)}/4")
        for f in existing_files:
            print(f"  ✓ {f}")
        
        if len(existing_files) >= 3:
            print("\n正在运行完整的四文件对比分析...")
            try:
                # 更新four_files_comparison.py以使用新的文件
                os.system('py updated_four_files_comparison.py')
            except:
                print("请手动运行对比分析")

if __name__ == "__main__":
    main()
