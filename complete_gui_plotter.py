#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优化版数据可视化程序 - 完整功能GUI版本
重写优化版本，包含所有核心功能，界面更友好，性能更优
版本: 2.0 - 全面重构优化版
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, colorchooser
import os
import sys
import threading
import tempfile
import shutil
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 依赖包检查和导入
class DependencyManager:
    """依赖管理器 - 智能检测和处理依赖包"""

    def __init__(self):
        self.dependencies = {
            'matplotlib': {'available': False, 'error': None, 'module': None},
            'pandas': {'available': False, 'error': None, 'module': None},
            'numpy': {'available': False, 'error': None, 'module': None},
            'openpyxl': {'available': False, 'error': None, 'module': None}
        }
        self.check_all_dependencies()

    def check_all_dependencies(self):
        """检查所有依赖包"""
        # 检查matplotlib
        try:
            import matplotlib.pyplot as plt
            from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg, NavigationToolbar2Tk
            import matplotlib.dates as mdates
            self.dependencies['matplotlib']['available'] = True
            self.dependencies['matplotlib']['module'] = {
                'plt': plt, 'FigureCanvasTkAgg': FigureCanvasTkAgg,
                'NavigationToolbar2Tk': NavigationToolbar2Tk, 'mdates': mdates
            }
        except ImportError as e:
            self.dependencies['matplotlib']['error'] = str(e)

        # 检查pandas
        try:
            import pandas as pd
            self.dependencies['pandas']['available'] = True
            self.dependencies['pandas']['module'] = pd
        except ImportError as e:
            self.dependencies['pandas']['error'] = str(e)

        # 检查numpy
        try:
            import numpy as np
            self.dependencies['numpy']['available'] = True
            self.dependencies['numpy']['module'] = np
        except ImportError as e:
            self.dependencies['numpy']['error'] = str(e)

        # 检查openpyxl
        try:
            import openpyxl
            self.dependencies['openpyxl']['available'] = True
            self.dependencies['openpyxl']['module'] = openpyxl
        except ImportError as e:
            self.dependencies['openpyxl']['error'] = str(e)

    def get_missing_dependencies(self):
        """获取缺失的依赖包列表"""
        return [name for name, info in self.dependencies.items() if not info['available']]

    def all_dependencies_available(self):
        """检查是否所有依赖都可用"""
        return all(info['available'] for info in self.dependencies.values())

    def get_module(self, name):
        """获取已导入的模块"""
        if self.dependencies[name]['available']:
            return self.dependencies[name]['module']
        return None


class LayoutManager:
    """布局管理器 - 处理响应式布局和UI组件管理"""

    def __init__(self, parent):
        self.parent = parent
        self.current_layout_mode = 'wide'
        self.last_window_width = 0
        self.columns_per_row = 4

    def determine_layout_mode(self, width):
        """根据窗口宽度确定布局模式"""
        for mode, config in self.parent.layout_config.items():
            if width < config['breakpoint']:
                return mode
        return 'ultra_wide'

    def get_columns_per_row(self, layout_mode):
        """获取当前布局模式下每行显示的列数"""
        return self.parent.layout_config.get(layout_mode, {}).get('columns_per_row', 4)

    def update_layout(self, force=False):
        """更新布局"""
        current_width = self.parent.root.winfo_width()

        if abs(current_width - self.last_window_width) > 50 or force:
            new_layout_mode = self.determine_layout_mode(current_width)

            if new_layout_mode != self.current_layout_mode or force:
                self.current_layout_mode = new_layout_mode
                self.columns_per_row = self.get_columns_per_row(new_layout_mode)
                self.parent.update_column_selection_layout()

            self.last_window_width = current_width


class DataProcessor:
    """数据处理器 - 处理文件读取、数据清理和类型转换"""

    def __init__(self, parent):
        self.parent = parent

    def load_file(self, file_path):
        """加载文件 - 支持CSV和Excel"""
        try:
            file_ext = os.path.splitext(file_path)[1].lower()

            if file_ext == '.csv':
                return self.load_csv(file_path)
            elif file_ext in ['.xlsx', '.xls']:
                return self.load_excel(file_path)
            else:
                raise ValueError(f"不支持的文件格式: {file_ext}")

        except Exception as e:
            raise Exception(f"文件加载失败: {str(e)}")

    def load_csv(self, file_path):
        """加载CSV文件"""
        encodings = ['utf-8', 'gbk', 'gb2312', 'utf-8-sig', 'latin1']

        for encoding in encodings:
            try:
                data = self.parent.pd.read_csv(
                    file_path,
                    encoding=encoding,
                    keep_default_na=True,
                    na_values=['', 'NA', 'N/A', 'null', '#N/A']
                )
                return data, ["CSV数据"]
            except UnicodeDecodeError:
                continue
            except Exception as e:
                if encoding == encodings[-1]:  # 最后一个编码也失败
                    raise e
                continue

        raise ValueError("无法识别文件编码")

    def load_excel(self, file_path):
        """加载Excel文件 - 增强兼容性"""
        # 获取工作表名称
        sheet_names = self.get_sheet_names(file_path)

        # 尝试多种方法加载第一个工作表
        methods = [
            self._load_excel_standard,
            self._load_excel_data_only,
            self._load_excel_manual
        ]

        for method in methods:
            try:
                data = method(file_path, sheet_names[0])
                if data is not None and not data.empty:
                    return data, sheet_names
            except Exception as e:
                continue

        raise Exception("无法读取Excel文件，请尝试将文件另存为CSV格式")

    def _load_excel_standard(self, file_path, sheet_name):
        """标准Excel加载方法"""
        return self.parent.pd.read_excel(file_path, sheet_name=sheet_name)

    def _load_excel_data_only(self, file_path, sheet_name):
        """使用data_only模式加载Excel"""
        return self.parent.pd.read_excel(
            file_path,
            sheet_name=sheet_name,
            engine='openpyxl',
            engine_kwargs={'data_only': True}
        )

    def _load_excel_manual(self, file_path, sheet_name):
        """手动提取Excel数据"""
        openpyxl = self.parent.dep_manager.get_module('openpyxl')
        if not openpyxl:
            raise ImportError("openpyxl not available")

        wb = openpyxl.load_workbook(file_path, data_only=True, read_only=True)
        ws = wb[sheet_name] if isinstance(sheet_name, str) else wb.worksheets[sheet_name]

        data = []
        for row in ws.iter_rows(values_only=True):
            if any(cell is not None for cell in row):
                data.append(row)

        if not data:
            raise ValueError("工作表为空")

        columns = [str(col) if col is not None else f'Column_{i}' for i, col in enumerate(data[0])]
        df = self.parent.pd.DataFrame(data[1:], columns=columns)
        return df.dropna(axis=1, how='all')

    def get_sheet_names(self, file_path):
        """获取Excel工作表名称"""
        try:
            openpyxl = self.parent.dep_manager.get_module('openpyxl')
            if openpyxl:
                wb = openpyxl.load_workbook(file_path, read_only=True)
                return wb.sheetnames
        except:
            pass

        try:
            excel_file = self.parent.pd.ExcelFile(file_path)
            return excel_file.sheet_names
        except:
            return ["Sheet1"]

    def process_data(self, data):
        """处理和清理数据"""
        if data is None or data.empty:
            return data

        # 清理列名
        data.columns = [str(col).strip() for col in data.columns]

        # 删除完全空的行和列
        data = data.dropna(how='all').dropna(axis=1, how='all')

        # 智能类型转换
        for col in data.columns:
            data[col] = self._smart_type_conversion(data[col])

        return data

    def _smart_type_conversion(self, series):
        """智能类型转换"""
        if series.dtype == 'object':
            # 尝试转换为数值
            numeric_series = self.parent.pd.to_numeric(series, errors='coerce')
            valid_ratio = numeric_series.notna().sum() / len(series)

            if valid_ratio > 0.7:  # 70%以上可转换为数值
                return numeric_series

            # 尝试转换为日期时间
            try:
                datetime_series = self.parent.pd.to_datetime(series, errors='coerce')
                valid_ratio = datetime_series.notna().sum() / len(series)
                if valid_ratio > 0.7:
                    return datetime_series
            except:
                pass

        return series

    def detect_time_column(self, data):
        """自动检测时间列"""
        if data is None or data.empty:
            return None

        time_keywords = ['时间', 'time', '日期', 'date', 'datetime', '时刻', 'timestamp']

        # 检查列名
        for col in data.columns:
            col_str = str(col).lower()
            if any(keyword in col_str for keyword in time_keywords):
                return col

        # 检查数据类型
        for col in data.columns:
            if 'datetime' in str(data[col].dtype).lower():
                return col

        return None


class ChartGenerator:
    """图表生成器 - 处理图表创建和样式设置"""

    def __init__(self, parent):
        self.parent = parent

    def create_chart(self, ax, data, selected_columns, time_col, chart_type, theme):
        """创建图表"""
        if data is None or data.empty or not selected_columns:
            self._create_empty_chart(ax)
            return

        # 准备数据
        plot_data = data.copy()

        # 处理时间列
        x_data = self._prepare_x_data(plot_data, time_col)

        # 应用主题
        self._apply_theme(theme)

        # 绘制图表
        self._plot_data(ax, plot_data, selected_columns, x_data, chart_type)

        # 设置图表样式
        self._set_chart_style(ax, time_col)

        # 优化布局
        self._optimize_layout()

    def _create_empty_chart(self, ax):
        """创建空图表"""
        ax.clear()
        ax.text(0.5, 0.5, '请选择数据文件和列开始绘图',
                horizontalalignment='center', verticalalignment='center',
                transform=ax.transAxes, fontsize=14, color='gray')
        ax.set_xticks([])
        ax.set_yticks([])

    def _prepare_x_data(self, data, time_col):
        """准备X轴数据"""
        if time_col and time_col in data.columns:
            try:
                data[time_col] = self.parent.pd.to_datetime(data[time_col])
                return data[time_col]
            except:
                pass
        return range(len(data))

    def _apply_theme(self, theme):
        """应用主题"""
        theme_config = self.parent.themes.get(theme, self.parent.themes['默认'])
        style = theme_config.get('style', 'default')

        try:
            if style != 'default':
                self.parent.plt.style.use(style)
        except:
            pass  # 如果样式不可用，使用默认样式

    def _plot_data(self, ax, data, selected_columns, x_data, chart_type):
        """绘制数据"""
        ax.clear()

        theme_colors = self.parent.themes[self.parent.theme_var.get()]['colors']

        for i, col in enumerate(selected_columns):
            if col not in data.columns:
                continue

            y_data = data[col].dropna()
            if len(y_data) == 0:
                continue

            # 获取颜色和标签
            color = self.parent.color_vars.get(col, theme_colors[i % len(theme_colors)])
            label = self.parent.label_vars.get(col, col)

            # 根据图表类型绘制
            if chart_type == '线图':
                ax.plot(x_data[:len(y_data)], y_data, color=color, label=label, linewidth=2, marker='o', markersize=4)
            elif chart_type == '散点图':
                ax.scatter(x_data[:len(y_data)], y_data, color=color, label=label, alpha=0.7, s=50)
            elif chart_type == '柱状图':
                ax.bar(x_data[:len(y_data)], y_data, color=color, label=label, alpha=0.8, width=0.8)
            elif chart_type == '面积图':
                ax.fill_between(x_data[:len(y_data)], y_data, color=color, label=label, alpha=0.6)

    def _set_chart_style(self, ax, time_col):
        """设置图表样式"""
        # 设置标题和标签
        ax.set_title(self.parent.title_var.get(), fontsize=14, fontweight='bold', pad=20)
        ax.set_ylabel(self.parent.ylabel_var.get(), fontsize=12)

        # 设置X轴标签
        if time_col:
            ax.set_xlabel('时间', fontsize=12)
            # 格式化时间轴
            try:
                import matplotlib.dates as mdates
                ax.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d'))
                ax.xaxis.set_major_locator(mdates.DayLocator(interval=max(1, len(ax.get_xticklabels()) // 10)))
                self.parent.plt.setp(ax.xaxis.get_majorticklabels(), rotation=45, ha='right')
            except:
                pass
        else:
            ax.set_xlabel('数据点', fontsize=12)

        # 设置图例
        if len(ax.get_lines()) > 1 or len(ax.collections) > 1:
            ax.legend(loc='best', frameon=True, fancybox=True, shadow=True)

        # 设置网格
        ax.grid(True, alpha=0.3, linestyle='--')

        # 优化刻度
        ax.tick_params(axis='both', which='major', labelsize=10)

    def _optimize_layout(self):
        """优化布局 - 简化版本，确保稳定显示"""
        try:
            # 使用固定的安全边距，确保所有元素可见
            self.parent.fig.subplots_adjust(
                left=0.12,    # 左边距
                bottom=0.25,  # 底边距 - 确保X轴标签可见
                right=0.95,   # 右边距
                top=0.90,     # 上边距
                wspace=0.2,   # 子图间距
                hspace=0.3
            )
        except Exception as e:
            print(f"布局优化失败: {e}")
            # 使用最基本的布局
            try:
                self.parent.fig.tight_layout(pad=2.0)
            except:
                pass

class OptimizedDataVisualizerGUI:
    """优化版数据可视化GUI - 完整功能，优化结构"""

    def __init__(self, root):
        print("🚀 优化版数据可视化程序启动 v2.0 🚀")
        print("✨ 全面重构：优化布局、增强功能、改进性能")

        self.root = root
        self.root.title("数据可视化程序 - 优化版 v2.0")
        self.root.geometry("1400x900")
        self.root.minsize(800, 600)

        # 初始化依赖管理器
        self.dep_manager = DependencyManager()

        # 检查依赖
        if not self.dep_manager.all_dependencies_available():
            self.show_dependency_error()
            return

        # 获取依赖模块
        self.plt = self.dep_manager.get_module('matplotlib')['plt']
        self.pd = self.dep_manager.get_module('pandas')
        self.np = self.dep_manager.get_module('numpy')

        # 数据相关变量
        self.data = None
        self.file_path = None
        self.sheet_names = []

        # GUI变量
        self.file_path_var = tk.StringVar()
        self.sheet_var = tk.StringVar()
        self.time_col_var = tk.StringVar()
        self.title_var = tk.StringVar(value="数据对比图")
        self.ylabel_var = tk.StringVar(value="数值")
        self.chart_type_var = tk.StringVar(value="线图")
        self.theme_var = tk.StringVar(value="默认")
        self.width_var = tk.StringVar(value="12")
        self.height_var = tk.StringVar(value="8")
        self.status_var = tk.StringVar(value="就绪 - 请选择数据文件")

        # 数据列选择变量 - 优化为字典结构
        self.column_vars = {}
        self.label_vars = {}
        self.color_vars = {}

        # 布局管理
        self.layout_manager = LayoutManager(self)

        # 数据处理器
        self.data_processor = DataProcessor(self)

        # 图表生成器
        self.chart_generator = ChartGenerator(self)

        # 初始化配置
        self.init_configuration()

        # 设置matplotlib
        self.setup_matplotlib()

        # 创建界面
        self.create_widgets()

    def init_configuration(self):
        """初始化配置"""
        # 颜色配置
        self.colors = ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728',
                      '#9467bd', '#8c564b', '#e377c2', '#7f7f7f', '#bcbd22', '#17becf']

        # 主题配置
        self.themes = {
            '默认': {'colors': self.colors, 'style': 'default'},
            '科学': {'colors': ['#0173b2', '#de8f05', '#029e73', '#cc78bc', '#d55e00'], 'style': 'seaborn-v0_8'},
            '深色': {'colors': ['#00d4ff', '#ff6b35', '#4ecdc4', '#ff9999', '#ffd700'], 'style': 'dark_background'},
            '简洁': {'colors': ['#2E86AB', '#A23B72', '#F18F01', '#C73E1D', '#592E83'], 'style': 'seaborn-v0_8-whitegrid'}
        }

        # 图表类型配置
        self.chart_types = ['线图', '散点图', '柱状图', '面积图', '组合图']

        # 布局配置
        self.layout_config = {
            'narrow': {'breakpoint': 900, 'columns_per_row': 2},
            'medium': {'breakpoint': 1200, 'columns_per_row': 3},
            'wide': {'breakpoint': 1500, 'columns_per_row': 4},
            'ultra_wide': {'breakpoint': float('inf'), 'columns_per_row': 6}
        }

    def show_dependency_error(self):
        """显示依赖错误并提供解决方案"""
        missing_deps = self.dep_manager.get_missing_dependencies()

        if not missing_deps:
            return

        # 创建错误窗口
        error_window = tk.Toplevel(self.root)
        error_window.title("依赖包缺失")
        error_window.geometry("700x600")
        error_window.transient(self.root)
        error_window.grab_set()

        # 主框架
        main_frame = ttk.Frame(error_window)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # 标题
        title_label = ttk.Label(main_frame, text="数据可视化程序需要以下依赖包：",
                               font=("Arial", 12, "bold"))
        title_label.pack(anchor=tk.W, pady=(0, 15))

        # 依赖状态显示
        deps_frame = ttk.LabelFrame(main_frame, text="依赖包状态", padding=10)
        deps_frame.pack(fill=tk.X, pady=(0, 15))

        for dep_name, dep_info in self.dep_manager.dependencies.items():
            status_frame = ttk.Frame(deps_frame)
            status_frame.pack(fill=tk.X, pady=2)

            if dep_info['available']:
                status_text = f"✓ {dep_name} (已安装)"
                color = "green"
            else:
                status_text = f"✗ {dep_name} (缺失)"
                color = "red"

            ttk.Label(status_frame, text=status_text, foreground=color).pack(side=tk.LEFT)

        # 解决方案
        solutions_frame = ttk.LabelFrame(main_frame, text="解决方案", padding=10)
        solutions_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 15))

        solutions_text = """推荐解决方案（按优先级排序）：

方法1：一键自动安装（推荐）
点击下方"自动安装"按钮，程序将自动安装所有依赖包

方法2：手动安装
在命令提示符中运行：
python -m pip install matplotlib pandas numpy openpyxl

方法3：使用国内镜像（网络慢时）
python -m pip install -i https://pypi.tuna.tsinghua.edu.cn/simple matplotlib pandas numpy openpyxl

方法4：使用Anaconda（推荐新手）
下载并安装Anaconda，自带所有科学计算包
网址：https://www.anaconda.com/

方法5：使用conda安装
conda install matplotlib pandas numpy openpyxl

注意事项：
- 安装完成后请重新启动程序
- 如果遇到权限问题，请以管理员身份运行命令提示符
- 建议使用虚拟环境管理Python包"""

        text_widget = tk.Text(solutions_frame, height=15, wrap=tk.WORD, font=("Consolas", 9))
        scrollbar = ttk.Scrollbar(solutions_frame, orient="vertical", command=text_widget.yview)
        text_widget.configure(yscrollcommand=scrollbar.set)

        text_widget.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        text_widget.insert(tk.END, solutions_text)
        text_widget.config(state=tk.DISABLED)

        # 按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X)

        ttk.Button(button_frame, text="自动安装",
                  command=lambda: self.auto_install_dependencies(error_window)).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="使用简化版",
                  command=lambda: self.start_simple_mode(error_window)).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="退出",
                  command=self.root.destroy).pack(side=tk.RIGHT)

    def auto_install_dependencies(self, parent_window):
        """自动安装依赖包"""
        # 创建安装进度窗口
        progress_window = tk.Toplevel(parent_window)
        progress_window.title("安装进度")
        progress_window.geometry("500x400")
        progress_window.transient(parent_window)
        progress_window.grab_set()

        # 进度显示
        progress_frame = ttk.Frame(progress_window)
        progress_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        ttk.Label(progress_frame, text="正在安装依赖包...", font=("Arial", 12, "bold")).pack(pady=(0, 10))

        progress_text = tk.Text(progress_frame, height=20, wrap=tk.WORD)
        progress_scrollbar = ttk.Scrollbar(progress_frame, orient="vertical", command=progress_text.yview)
        progress_text.configure(yscrollcommand=progress_scrollbar.set)

        progress_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        progress_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 在新线程中执行安装
        def install_thread():
            try:
                import subprocess
                packages = ['matplotlib', 'pandas', 'numpy', 'openpyxl']

                progress_text.insert(tk.END, "开始安装依赖包...\n")
                progress_text.update()

                for package in packages:
                    progress_text.insert(tk.END, f"\n正在安装 {package}...\n")
                    progress_text.update()

                    # 尝试多种安装方法
                    install_commands = [
                        [sys.executable, "-m", "pip", "install", "--user", package],
                        [sys.executable, "-m", "pip", "install", package],
                        ["pip", "install", "--user", package],
                        ["pip", "install", package]
                    ]

                    success = False
                    for cmd in install_commands:
                        try:
                            result = subprocess.run(cmd, capture_output=True, text=True, timeout=120)
                            if result.returncode == 0:
                                progress_text.insert(tk.END, f"✓ {package} 安装成功\n")
                                success = True
                                break
                            else:
                                progress_text.insert(tk.END, f"命令失败: {' '.join(cmd)}\n")
                        except Exception as e:
                            progress_text.insert(tk.END, f"命令错误: {e}\n")
                        progress_text.update()

                    if not success:
                        progress_text.insert(tk.END, f"✗ {package} 安装失败\n")
                    progress_text.update()

                progress_text.insert(tk.END, "\n安装完成！请重新启动程序。\n")
                messagebox.showinfo("完成", "依赖包安装完成！\n请重新启动程序。")

            except Exception as e:
                progress_text.insert(tk.END, f"\n安装过程出错: {e}\n")
                messagebox.showerror("安装失败", f"自动安装失败: {e}")

            finally:
                progress_window.destroy()
                parent_window.destroy()
                self.root.destroy()

        thread = threading.Thread(target=install_thread, daemon=True)
        thread.start()

    def start_simple_mode(self, error_window):
        """启动简化模式"""
        error_window.destroy()
        self.root.destroy()

        # 这里可以启动一个简化版本或显示基本功能
        messagebox.showinfo("简化模式", "简化模式功能正在开发中。\n请安装依赖包以使用完整功能。")

    def setup_matplotlib(self):
        """设置matplotlib中文字体和样式"""
        try:
            # 设置中文字体
            self.plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS', 'DejaVu Sans']
            self.plt.rcParams['axes.unicode_minus'] = False

            # 设置默认样式
            self.plt.rcParams['figure.facecolor'] = 'white'
            self.plt.rcParams['axes.facecolor'] = 'white'
            self.plt.rcParams['savefig.facecolor'] = 'white'

            # 设置图表质量
            self.plt.rcParams['figure.dpi'] = 100
            self.plt.rcParams['savefig.dpi'] = 300

            print("✓ matplotlib配置完成")
        except Exception as e:
            print(f"matplotlib配置失败: {e}")

    def create_widgets(self):
        """创建主界面组件"""
        # 创建主框架
        self.main_frame = ttk.Frame(self.root)
        self.main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 创建左右分栏布局
        self.create_left_panel()
        self.create_right_panel()

        # 创建状态栏
        self.create_status_bar()

        # 绑定窗口大小变化事件
        self.root.bind('<Configure>', self.on_window_resize)

        print("✓ 界面创建完成")

    def create_left_panel(self):
        """创建左侧控制面板"""
        # 左侧面板框架
        self.left_frame = ttk.Frame(self.main_frame, width=400)
        self.left_frame.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 10))
        self.left_frame.pack_propagate(False)

        # 1. 文件选择区域
        self.create_file_selection_area()

        # 2. 数据映射区域
        self.create_data_mapping_area()

        # 3. 图表配置区域
        self.create_chart_config_area()

        # 4. 操作按钮区域
        self.create_action_buttons_area()

    def create_file_selection_area(self):
        """创建文件选择区域"""
        file_frame = ttk.LabelFrame(self.left_frame, text="📁 数据源选择", padding=10)
        file_frame.pack(fill=tk.X, pady=(0, 10))

        # 文件路径选择
        ttk.Label(file_frame, text="数据文件:").pack(anchor=tk.W)

        path_frame = ttk.Frame(file_frame)
        path_frame.pack(fill=tk.X, pady=(5, 10))

        self.file_entry = ttk.Entry(path_frame, textvariable=self.file_path_var, state='readonly')
        self.file_entry.pack(side=tk.LEFT, fill=tk.X, expand=True)

        ttk.Button(path_frame, text="浏览", command=self.browse_file).pack(side=tk.RIGHT, padx=(5, 0))

        # 工作表选择
        ttk.Label(file_frame, text="工作表:").pack(anchor=tk.W)
        self.sheet_combo = ttk.Combobox(file_frame, textvariable=self.sheet_var, state='readonly')
        self.sheet_combo.pack(fill=tk.X, pady=(5, 0))
        self.sheet_combo.bind('<<ComboboxSelected>>', self.on_sheet_changed)

    def create_data_mapping_area(self):
        """创建数据映射区域 - 优化为多列布局"""
        mapping_frame = ttk.LabelFrame(self.left_frame, text="📊 数据列映射", padding=10)
        mapping_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))

        # 时间列选择
        ttk.Label(mapping_frame, text="时间列:").pack(anchor=tk.W)
        self.time_combo = ttk.Combobox(mapping_frame, textvariable=self.time_col_var, state='readonly')
        self.time_combo.pack(fill=tk.X, pady=(5, 15))
        self.time_combo.bind('<<ComboboxSelected>>', self.update_preview)

        # 数据列选择标题
        columns_label_frame = ttk.Frame(mapping_frame)
        columns_label_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Label(columns_label_frame, text="选择要绘制的数据列:", font=("Arial", 10, "bold")).pack(side=tk.LEFT)
        ttk.Button(columns_label_frame, text="全选", command=self.select_all_columns).pack(side=tk.RIGHT, padx=(5, 0))
        ttk.Button(columns_label_frame, text="清空", command=self.clear_all_columns).pack(side=tk.RIGHT)

        # 创建滚动区域用于数据列选择 - 多列网格布局
        self.create_columns_scroll_area(mapping_frame)

    def create_columns_scroll_area(self, parent):
        """创建数据列选择的滚动区域 - 多列网格布局"""
        # 滚动框架
        canvas_frame = ttk.Frame(parent)
        canvas_frame.pack(fill=tk.BOTH, expand=True)

        self.columns_canvas = tk.Canvas(canvas_frame, height=200)
        scrollbar = ttk.Scrollbar(canvas_frame, orient="vertical", command=self.columns_canvas.yview)
        self.columns_frame = ttk.Frame(self.columns_canvas)

        self.columns_canvas.configure(yscrollcommand=scrollbar.set)
        self.columns_canvas.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        self.columns_canvas.create_window((0, 0), window=self.columns_frame, anchor="nw")
        self.columns_frame.bind("<Configure>",
                               lambda e: self.columns_canvas.configure(scrollregion=self.columns_canvas.bbox("all")))

        # 绑定鼠标滚轮事件
        self.columns_canvas.bind("<MouseWheel>", self._on_mousewheel)

    def _on_mousewheel(self, event):
        """处理鼠标滚轮事件"""
        self.columns_canvas.yview_scroll(int(-1*(event.delta/120)), "units")

    def create_chart_config_area(self):
        """创建图表配置区域"""
        config_frame = ttk.LabelFrame(self.left_frame, text="🎨 图表配置", padding=10)
        config_frame.pack(fill=tk.X, pady=(0, 10))

        # 图表标题
        ttk.Label(config_frame, text="图表标题:").pack(anchor=tk.W)
        title_entry = ttk.Entry(config_frame, textvariable=self.title_var)
        title_entry.pack(fill=tk.X, pady=(5, 10))
        title_entry.bind('<KeyRelease>', lambda e: self.update_preview())

        # Y轴标签
        ttk.Label(config_frame, text="Y轴标签:").pack(anchor=tk.W)
        ylabel_entry = ttk.Entry(config_frame, textvariable=self.ylabel_var)
        ylabel_entry.pack(fill=tk.X, pady=(5, 10))
        ylabel_entry.bind('<KeyRelease>', lambda e: self.update_preview())

        # 图表类型和主题 - 并排布局
        type_theme_frame = ttk.Frame(config_frame)
        type_theme_frame.pack(fill=tk.X, pady=(0, 10))

        # 图表类型
        type_frame = ttk.Frame(type_theme_frame)
        type_frame.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 5))
        ttk.Label(type_frame, text="图表类型:").pack(anchor=tk.W)
        chart_type_combo = ttk.Combobox(type_frame, textvariable=self.chart_type_var,
                                       values=self.chart_types, state='readonly')
        chart_type_combo.pack(fill=tk.X, pady=(5, 0))
        chart_type_combo.bind('<<ComboboxSelected>>', self.update_preview)

        # 颜色主题
        theme_frame = ttk.Frame(type_theme_frame)
        theme_frame.pack(side=tk.RIGHT, fill=tk.X, expand=True, padx=(5, 0))
        ttk.Label(theme_frame, text="颜色主题:").pack(anchor=tk.W)
        theme_combo = ttk.Combobox(theme_frame, textvariable=self.theme_var,
                                  values=list(self.themes.keys()), state='readonly')
        theme_combo.pack(fill=tk.X, pady=(5, 0))
        theme_combo.bind('<<ComboboxSelected>>', self.update_preview)

        # 图表尺寸
        size_frame = ttk.Frame(config_frame)
        size_frame.pack(fill=tk.X)

        ttk.Label(size_frame, text="图表尺寸:").pack(anchor=tk.W)
        size_input_frame = ttk.Frame(size_frame)
        size_input_frame.pack(fill=tk.X, pady=(5, 0))

        ttk.Label(size_input_frame, text="宽:").pack(side=tk.LEFT)
        width_entry = ttk.Entry(size_input_frame, textvariable=self.width_var, width=8)
        width_entry.pack(side=tk.LEFT, padx=(5, 10))
        width_entry.bind('<KeyRelease>', lambda e: self.update_preview())

        ttk.Label(size_input_frame, text="高:").pack(side=tk.LEFT)
        height_entry = ttk.Entry(size_input_frame, textvariable=self.height_var, width=8)
        height_entry.pack(side=tk.LEFT, padx=(5, 0))
        height_entry.bind('<KeyRelease>', lambda e: self.update_preview())

    def create_action_buttons_area(self):
        """创建操作按钮区域"""
        button_frame = ttk.LabelFrame(self.left_frame, text="🔧 操作", padding=10)
        button_frame.pack(fill=tk.X)

        # 主要操作按钮
        ttk.Button(button_frame, text="🎯 生成图表", command=self.generate_chart).pack(fill=tk.X, pady=(0, 5))
        ttk.Button(button_frame, text="💾 保存图片", command=self.save_chart).pack(fill=tk.X, pady=(0, 5))
        ttk.Button(button_frame, text="🔍 全屏预览", command=self.show_fullscreen).pack(fill=tk.X, pady=(0, 5))

        # 工具按钮
        tools_frame = ttk.Frame(button_frame)
        tools_frame.pack(fill=tk.X, pady=(5, 0))

        ttk.Button(tools_frame, text="📄 转换CSV", command=self.convert_to_csv).pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 2))
        ttk.Button(tools_frame, text="🔄 重置", command=self.reset_all).pack(side=tk.RIGHT, fill=tk.X, expand=True, padx=(2, 0))

    def create_right_panel(self):
        """创建右侧预览面板"""
        # 右侧面板框架
        self.right_frame = ttk.Frame(self.main_frame)
        self.right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)

        # 预览区域
        preview_frame = ttk.LabelFrame(self.right_frame, text="📈 实时预览", padding=5)
        preview_frame.pack(fill=tk.BOTH, expand=True)

        # 创建matplotlib图形
        self.create_matplotlib_canvas(preview_frame)

    def create_matplotlib_canvas(self, parent):
        """创建matplotlib画布"""
        try:
            # 获取matplotlib组件
            FigureCanvasTkAgg = self.dep_manager.get_module('matplotlib')['FigureCanvasTkAgg']
            NavigationToolbar2Tk = self.dep_manager.get_module('matplotlib')['NavigationToolbar2Tk']

            # 创建图形
            self.fig, self.ax = self.plt.subplots(figsize=(10, 6), dpi=100)
            self.fig.patch.set_facecolor('white')

            # 创建画布
            self.canvas = FigureCanvasTkAgg(self.fig, parent)
            canvas_widget = self.canvas.get_tk_widget()
            canvas_widget.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

            # 创建工具栏
            toolbar_frame = ttk.Frame(parent)
            toolbar_frame.pack(side=tk.BOTTOM, fill=tk.X, pady=(5, 0))
            toolbar = NavigationToolbar2Tk(self.canvas, toolbar_frame)
            toolbar.update()

            # 设置初始布局
            self.fig.subplots_adjust(left=0.12, bottom=0.25, right=0.95, top=0.90)

            # 显示初始消息
            self.ax.text(0.5, 0.5, '请选择数据文件开始',
                        horizontalalignment='center', verticalalignment='center',
                        transform=self.ax.transAxes, fontsize=16, color='gray')
            self.ax.set_xticks([])
            self.ax.set_yticks([])

            self.canvas.draw()

            # 绑定画布大小变化事件
            canvas_widget.bind('<Configure>', self.on_canvas_resize)

        except Exception as e:
            print(f"创建matplotlib画布失败: {e}")
            # 创建错误提示标签
            error_label = ttk.Label(parent, text=f"图表预览不可用: {e}",
                                   foreground="red", font=("Arial", 12))
            error_label.pack(expand=True)

    def create_status_bar(self):
        """创建状态栏"""
        status_frame = ttk.Frame(self.main_frame)
        status_frame.pack(side=tk.BOTTOM, fill=tk.X, pady=(10, 0))

        # 状态标签
        ttk.Label(status_frame, text="状态:").pack(side=tk.LEFT)
        status_label = ttk.Label(status_frame, textvariable=self.status_var,
                               foreground="blue", font=("Arial", 9))
        status_label.pack(side=tk.LEFT, padx=(5, 0))

        # 分隔符
        ttk.Separator(status_frame, orient='vertical').pack(side=tk.LEFT, fill=tk.Y, padx=(20, 10))

        # 帮助按钮
        ttk.Button(status_frame, text="❓ 帮助", command=self.show_help).pack(side=tk.RIGHT)

    def on_window_resize(self, event):
        """窗口大小变化事件处理"""
        if event.widget == self.root:
            self.layout_manager.update_layout()

    def on_canvas_resize(self, event):
        """画布大小变化事件处理"""
        if hasattr(self, 'fig') and self.fig:
            try:
                # 获取新的画布尺寸
                width_px = event.width
                height_px = event.height

                if width_px > 100 and height_px > 100:
                    # 转换为英寸
                    dpi = self.fig.get_dpi()
                    width_inch = max(3, (width_px - 20) / dpi)
                    height_inch = max(2, (height_px - 20) / dpi)

                    # 更新图表尺寸
                    self.fig.set_size_inches(width_inch, height_inch, forward=True)

                    # 重新绘制
                    self.canvas.draw_idle()

            except Exception as e:
                print(f"画布调整大小失败: {e}")

    def browse_file(self):
        """浏览并选择文件"""
        file_path = filedialog.askopenfilename(
            title="选择数据文件",
            filetypes=[
                ("所有支持格式", "*.csv;*.xlsx;*.xls"),
                ("CSV文件", "*.csv"),
                ("Excel文件", "*.xlsx;*.xls"),
                ("所有文件", "*.*")
            ]
        )

        if file_path:
            self.file_path = file_path
            self.file_path_var.set(os.path.basename(file_path))
            self.load_file()

    def load_file(self):
        """加载文件"""
        try:
            self.status_var.set("正在加载文件...")
            self.root.update()

            # 使用数据处理器加载文件
            self.data, self.sheet_names = self.data_processor.load_file(self.file_path)

            # 更新工作表选择
            self.sheet_combo['values'] = self.sheet_names
            self.sheet_var.set(self.sheet_names[0])

            # 处理数据
            self.data = self.data_processor.process_data(self.data)

            # 更新界面
            self.update_column_selection()

            # 自动检测时间列
            time_col = self.data_processor.detect_time_column(self.data)
            if time_col:
                self.time_col_var.set(time_col)

            # 更新预览
            self.update_preview()

            self.status_var.set(f"文件加载成功！数据形状: {self.data.shape}")
            messagebox.showinfo("成功", f"文件加载成功！\n数据形状: {self.data.shape}")

        except Exception as e:
            print(f"文件加载错误: {e}")
            self.status_var.set("文件加载失败")
            messagebox.showerror("加载失败", f"文件加载失败: {str(e)}")

    def on_sheet_changed(self, event=None):
        """工作表改变时的处理"""
        if self.file_path and self.sheet_var.get():
            try:
                # 重新加载指定工作表
                file_ext = os.path.splitext(self.file_path)[1].lower()
                if file_ext in ['.xlsx', '.xls']:
                    self.data = self.data_processor._load_excel_standard(self.file_path, self.sheet_var.get())
                    self.data = self.data_processor.process_data(self.data)
                    self.update_column_selection()
                    self.update_preview()
            except Exception as e:
                messagebox.showerror("错误", f"工作表切换失败: {str(e)}")

    def update_column_selection(self):
        """更新数据列选择 - 多列网格布局"""
        if self.data is None:
            return

        # 清除现有组件
        for widget in self.columns_frame.winfo_children():
            widget.destroy()

        # 清除变量
        self.column_vars.clear()
        self.label_vars.clear()
        self.color_vars.clear()

        # 获取数值列
        numeric_columns = []
        for col in self.data.columns:
            if self.pd.api.types.is_numeric_dtype(self.data[col]):
                numeric_columns.append(col)

        if not numeric_columns:
            ttk.Label(self.columns_frame, text="未找到数值列", foreground="red").pack()
            return

        # 创建多列网格布局
        self.create_column_grid(numeric_columns)

        # 更新时间列选择
        all_columns = list(self.data.columns)
        self.time_combo['values'] = [''] + all_columns

    def create_column_grid(self, columns):
        """创建数据列的网格布局"""
        columns_per_row = self.layout_manager.columns_per_row

        for i, col in enumerate(columns):
            row = i // columns_per_row
            column = i % columns_per_row

            # 创建列框架
            col_frame = ttk.Frame(self.columns_frame, relief='solid', borderwidth=1)
            col_frame.grid(row=row, column=column, padx=2, pady=2, sticky='ew')

            # 配置列权重
            self.columns_frame.grid_columnconfigure(column, weight=1)

            # 复选框
            var = tk.BooleanVar()
            self.column_vars[col] = var
            checkbox = ttk.Checkbutton(col_frame, text=col[:15] + ('...' if len(col) > 15 else ''),
                                     variable=var, command=self.update_preview)
            checkbox.pack(anchor=tk.W, padx=5, pady=2)

            # 标签输入框
            label_var = tk.StringVar(value=col)
            self.label_vars[col] = label_var
            label_entry = ttk.Entry(col_frame, textvariable=label_var, width=12, font=("Arial", 8))
            label_entry.pack(fill=tk.X, padx=5, pady=2)
            label_entry.bind('<KeyRelease>', lambda e: self.update_preview())

            # 颜色选择按钮
            color = self.colors[i % len(self.colors)]
            self.color_vars[col] = color
            color_button = tk.Button(col_frame, text="●", fg=color, bg=color,
                                   width=3, height=1, relief='raised',
                                   command=lambda c=col: self.choose_color(c))
            color_button.pack(pady=2)

    def update_column_selection_layout(self):
        """更新数据列选择布局"""
        if hasattr(self, 'data') and self.data is not None:
            self.update_column_selection()

    def choose_color(self, column):
        """选择颜色"""
        color = colorchooser.askcolor(title=f"选择 {column} 的颜色")[1]
        if color:
            self.color_vars[column] = color
            self.update_preview()

    def select_all_columns(self):
        """全选所有列"""
        for var in self.column_vars.values():
            var.set(True)
        self.update_preview()

    def clear_all_columns(self):
        """清空所有列选择"""
        for var in self.column_vars.values():
            var.set(False)
        self.update_preview()

    def update_preview(self, event=None):
        """更新图表预览"""
        if not hasattr(self, 'ax') or self.data is None:
            return

        try:
            # 获取选中的列
            selected_columns = [col for col, var in self.column_vars.items() if var.get()]

            if not selected_columns:
                self.ax.clear()
                self.ax.text(0.5, 0.5, '请选择要绘制的数据列',
                            horizontalalignment='center', verticalalignment='center',
                            transform=self.ax.transAxes, fontsize=14, color='gray')
                self.ax.set_xticks([])
                self.ax.set_yticks([])
                self.canvas.draw()
                return

            # 使用图表生成器创建图表
            self.chart_generator.create_chart(
                self.ax,
                self.data,
                selected_columns,
                self.time_col_var.get(),
                self.chart_type_var.get(),
                self.theme_var.get()
            )

            self.canvas.draw()

        except Exception as e:
            print(f"预览更新失败: {e}")
            self.ax.clear()
            self.ax.text(0.5, 0.5, f'预览更新失败: {str(e)}',
                        horizontalalignment='center', verticalalignment='center',
                        transform=self.ax.transAxes, fontsize=12, color='red')
            self.canvas.draw()

    def generate_chart(self):
        """生成完整图表"""
        if self.data is None:
            messagebox.showwarning("警告", "请先加载数据文件")
            return

        selected_columns = [col for col, var in self.column_vars.items() if var.get()]
        if not selected_columns:
            messagebox.showwarning("警告", "请选择要绘制的数据列")
            return

        try:
            # 获取图表尺寸
            width = float(self.width_var.get())
            height = float(self.height_var.get())

            # 创建新图表
            fig, ax = self.plt.subplots(figsize=(width, height), dpi=150)

            # 使用图表生成器创建图表
            self.chart_generator.create_chart(
                ax,
                self.data,
                selected_columns,
                self.time_col_var.get(),
                self.chart_type_var.get(),
                self.theme_var.get()
            )

            # 显示图表
            self.plt.show()

            self.status_var.set("图表生成成功")

        except Exception as e:
            print(f"图表生成失败: {e}")
            messagebox.showerror("错误", f"图表生成失败: {str(e)}")

    def save_chart(self):
        """保存图表"""
        if self.data is None:
            messagebox.showwarning("警告", "请先加载数据文件")
            return

        selected_columns = [col for col, var in self.column_vars.items() if var.get()]
        if not selected_columns:
            messagebox.showwarning("警告", "请选择要绘制的数据列")
            return

        # 选择保存路径
        file_path = filedialog.asksaveasfilename(
            title="保存图表",
            defaultextension=".png",
            filetypes=[
                ("PNG图片", "*.png"),
                ("JPG图片", "*.jpg"),
                ("PDF文件", "*.pdf"),
                ("SVG矢量图", "*.svg"),
                ("所有文件", "*.*")
            ]
        )

        if not file_path:
            return

        try:
            # 获取图表尺寸
            width = float(self.width_var.get())
            height = float(self.height_var.get())

            # 创建高质量图表
            fig, ax = self.plt.subplots(figsize=(width, height), dpi=300)

            # 使用图表生成器创建图表
            self.chart_generator.create_chart(
                ax,
                self.data,
                selected_columns,
                self.time_col_var.get(),
                self.chart_type_var.get(),
                self.theme_var.get()
            )

            # 保存图表
            fig.savefig(file_path, dpi=300, bbox_inches='tight',
                       facecolor='white', edgecolor='none')
            self.plt.close(fig)

            self.status_var.set(f"图表已保存: {os.path.basename(file_path)}")
            messagebox.showinfo("成功", f"图表已保存到:\n{file_path}")

        except Exception as e:
            print(f"图表保存失败: {e}")
            messagebox.showerror("错误", f"图表保存失败: {str(e)}")

    def show_fullscreen(self):
        """全屏预览图表"""
        if self.data is None:
            messagebox.showwarning("警告", "请先加载数据文件")
            return

        selected_columns = [col for col, var in self.column_vars.items() if var.get()]
        if not selected_columns:
            messagebox.showwarning("警告", "请选择要绘制的数据列")
            return

        try:
            # 创建全屏窗口
            fullscreen_window = tk.Toplevel(self.root)
            fullscreen_window.title("全屏预览")
            fullscreen_window.state('zoomed')  # Windows下最大化

            # 获取屏幕尺寸
            screen_width = fullscreen_window.winfo_screenwidth()
            screen_height = fullscreen_window.winfo_screenheight()

            # 创建图表
            fig, ax = self.plt.subplots(figsize=(screen_width/100, screen_height/100), dpi=100)

            # 使用图表生成器创建图表
            self.chart_generator.create_chart(
                ax,
                self.data,
                selected_columns,
                self.time_col_var.get(),
                self.chart_type_var.get(),
                self.theme_var.get()
            )

            # 创建画布
            FigureCanvasTkAgg = self.dep_manager.get_module('matplotlib')['FigureCanvasTkAgg']
            canvas = FigureCanvasTkAgg(fig, fullscreen_window)
            canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)

            # 添加关闭按钮
            close_button = ttk.Button(fullscreen_window, text="关闭",
                                    command=lambda: [self.plt.close(fig), fullscreen_window.destroy()])
            close_button.pack(side=tk.BOTTOM, pady=10)

            canvas.draw()

        except Exception as e:
            print(f"全屏预览失败: {e}")
            messagebox.showerror("错误", f"全屏预览失败: {str(e)}")

    def convert_to_csv(self):
        """转换当前文件为CSV格式"""
        if not self.file_path:
            messagebox.showwarning("警告", "请先选择文件")
            return

        file_ext = os.path.splitext(self.file_path)[1].lower()
        if file_ext == '.csv':
            messagebox.showinfo("提示", "当前文件已经是CSV格式")
            return

        if file_ext not in ['.xlsx', '.xls']:
            messagebox.showwarning("警告", "只支持Excel文件转换为CSV")
            return

        # 选择保存路径
        csv_path = filedialog.asksaveasfilename(
            title="保存CSV文件",
            defaultextension=".csv",
            filetypes=[("CSV文件", "*.csv"), ("所有文件", "*.*")]
        )

        if not csv_path:
            return

        try:
            # 转换并保存
            self.data.to_csv(csv_path, index=False, encoding='utf-8-sig')

            self.status_var.set(f"CSV文件已保存: {os.path.basename(csv_path)}")
            messagebox.showinfo("成功", f"CSV文件已保存到:\n{csv_path}")

        except Exception as e:
            print(f"CSV转换失败: {e}")
            messagebox.showerror("错误", f"CSV转换失败: {str(e)}")

    def reset_all(self):
        """重置所有设置"""
        # 重置数据
        self.data = None
        self.file_path = None
        self.sheet_names = []

        # 重置界面变量
        self.file_path_var.set("")
        self.sheet_var.set("")
        self.time_col_var.set("")
        self.title_var.set("数据对比图")
        self.ylabel_var.set("数值")
        self.chart_type_var.set("线图")
        self.theme_var.set("默认")
        self.width_var.set("12")
        self.height_var.set("8")
        self.status_var.set("就绪 - 请选择数据文件")

        # 清除数据列选择
        self.column_vars.clear()
        self.label_vars.clear()
        self.color_vars.clear()

        # 清除界面组件
        if hasattr(self, 'columns_frame'):
            for widget in self.columns_frame.winfo_children():
                widget.destroy()

        # 重置工作表选择
        if hasattr(self, 'sheet_combo'):
            self.sheet_combo['values'] = []

        if hasattr(self, 'time_combo'):
            self.time_combo['values'] = []

        # 重置图表
        if hasattr(self, 'ax'):
            self.ax.clear()
            self.ax.text(0.5, 0.5, '请选择数据文件开始',
                        horizontalalignment='center', verticalalignment='center',
                        transform=self.ax.transAxes, fontsize=16, color='gray')
            self.ax.set_xticks([])
            self.ax.set_yticks([])
            self.canvas.draw()

        messagebox.showinfo("重置", "所有设置已重置")

    def show_help(self):
        """显示帮助信息"""
        help_window = tk.Toplevel(self.root)
        help_window.title("使用帮助")
        help_window.geometry("600x500")
        help_window.transient(self.root)

        help_text = """数据可视化程序使用帮助

📁 数据源选择：
• 支持CSV和Excel文件格式
• Excel文件支持多工作表选择
• 自动检测文件编码和数据类型

📊 数据列映射：
• 自动识别数值列用于绘图
• 支持多列选择，网格布局显示
• 可自定义列标签和颜色
• 支持时间列自动检测

🎨 图表配置：
• 多种图表类型：线图、散点图、柱状图、面积图
• 多种颜色主题：默认、科学、深色、简洁
• 可调整图表标题、Y轴标签和尺寸

🔧 操作功能：
• 实时预览：选择数据后自动更新预览
• 生成图表：创建独立的高质量图表窗口
• 保存图片：支持PNG、JPG、PDF、SVG格式
• 全屏预览：全屏显示图表
• CSV转换：将Excel文件转换为CSV格式

💡 使用技巧：
• 程序支持响应式布局，会根据窗口大小调整界面
• 数据列选择区域支持滚动，可处理大量数据列
• 图表预览会自动优化布局，确保所有元素可见
• 支持鼠标滚轮在数据列选择区域滚动

❓ 常见问题：
• 如果图表显示不完整，尝试调整窗口大小
• Excel文件读取失败时，建议转换为CSV格式
• 中文显示异常时，检查系统字体设置

版本：2.0 - 优化重构版
"""

        text_widget = tk.Text(help_window, wrap=tk.WORD, font=("Arial", 10))
        scrollbar = ttk.Scrollbar(help_window, orient="vertical", command=text_widget.yview)
        text_widget.configure(yscrollcommand=scrollbar.set)

        text_widget.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=10, pady=10)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y, pady=10)

        text_widget.insert(tk.END, help_text)
        text_widget.config(state=tk.DISABLED)


# 主程序入口
def main():
    """主程序入口"""
    try:
        # 创建主窗口
        root = tk.Tk()

        # 设置窗口图标（如果有的话）
        try:
            # root.iconbitmap('icon.ico')  # 如果有图标文件
            pass
        except:
            pass

        # 创建应用程序
        app = OptimizedDataVisualizerGUI(root)

        # 启动主循环
        root.mainloop()

    except Exception as e:
        print(f"程序启动失败: {e}")
        import traceback
        traceback.print_exc()

        # 显示错误对话框
        try:
            messagebox.showerror("启动失败", f"程序启动失败:\n{str(e)}")
        except:
            print("无法显示错误对话框")


if __name__ == "__main__":
    main()


        """创建响应式GUI组件"""
        # 创建主框架
        self.main_frame = ttk.Frame(self.root)
        self.main_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # 初始化响应式参数
        self.current_layout_mode = 'wide'  # 'wide', 'medium', 'narrow'
        self.last_window_width = 0
        self.columns_per_row = 3
        self.layout_breakpoints = {
            'narrow': 900,   # 窄屏幕阈值
            'medium': 1200,  # 中等屏幕阈值
            'wide': 1500     # 宽屏幕阈值
        }

        # 创建响应式布局
        self.create_responsive_layout()

        # 绑定窗口大小变化事件
        self.root.bind('<Configure>', self.on_window_resize)

        # 设置最小窗口尺寸
        self.root.minsize(800, 600)

    def create_responsive_layout(self):
        """创建响应式布局"""
        # 清除现有布局
        for widget in self.main_frame.winfo_children():
            widget.destroy()

        # 获取当前窗口宽度
        window_width = self.root.winfo_width()

        # 确定布局模式
        layout_mode = self.determine_layout_mode(window_width)

        if layout_mode == 'narrow':
            self.create_narrow_layout()
        elif layout_mode == 'medium':
            self.create_medium_layout()
        else:
            self.create_wide_layout()

        self.current_layout_mode = layout_mode

    def determine_layout_mode(self, width):
        """根据窗口宽度确定布局模式"""
        if width < self.layout_breakpoints['narrow']:
            return 'narrow'
        elif width < self.layout_breakpoints['medium']:
            return 'medium'
        else:
            return 'wide'

    def create_narrow_layout(self):
        """创建窄屏布局（垂直堆叠）"""
        # 创建滚动容器
        canvas = tk.Canvas(self.main_frame)
        scrollbar = ttk.Scrollbar(self.main_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)

        canvas.configure(yscrollcommand=scrollbar.set)
        canvas.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        scrollable_frame.bind("<Configure>", lambda e: canvas.configure(scrollregion=canvas.bbox("all")))

        # 垂直布局：控制面板在上，预览在下
        self.create_control_panel(scrollable_frame)

        # 预览区域
        preview_frame = ttk.LabelFrame(scrollable_frame, text="图表预览", padding=10)
        preview_frame.pack(fill=tk.BOTH, expand=True, pady=(10, 0))
        self.create_preview_area(preview_frame)

        # 状态栏
        self.create_status_bar(scrollable_frame)

        # 设置数据列每行显示数量
        self.columns_per_row = 2

    def create_medium_layout(self):
        """创建中等屏幕布局（左右分栏，控制面板较宽）"""
        # 左侧控制面板（占40%宽度）
        left_frame = ttk.Frame(self.main_frame)
        left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=False, padx=(0, 5))

        # 右侧预览区域（占60%宽度）
        right_frame = ttk.Frame(self.main_frame)
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)

        # 配置权重
        self.main_frame.grid_columnconfigure(0, weight=2)  # 左侧
        self.main_frame.grid_columnconfigure(1, weight=3)  # 右侧

        self.create_control_panel(left_frame)
        self.create_preview_area(right_frame)

        # 状态栏
        status_frame = ttk.Frame(self.main_frame)
        status_frame.pack(side=tk.BOTTOM, fill=tk.X, pady=(5, 0))
        self.create_status_bar(status_frame)

        # 设置数据列每行显示数量
        self.columns_per_row = 3

    def create_wide_layout(self):
        """创建宽屏布局（左右分栏，预览区域更大）"""
        # 左侧控制面板（固定宽度）
        left_frame = ttk.Frame(self.main_frame, width=450)
        left_frame.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 10))
        left_frame.pack_propagate(False)

        # 右侧预览区域
        right_frame = ttk.Frame(self.main_frame)
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)

        self.create_control_panel(left_frame)
        self.create_preview_area(right_frame)

        # 状态栏
        status_frame = ttk.Frame(self.main_frame)
        status_frame.pack(side=tk.BOTTOM, fill=tk.X, pady=(5, 0))
        self.create_status_bar(status_frame)

        # 设置数据列每行显示数量
        self.columns_per_row = 4

    def on_window_resize(self, event):
        """窗口大小变化事件处理"""
        # 只处理主窗口的resize事件
        if event.widget != self.root:
            return

        current_width = self.root.winfo_width()

        # 避免频繁重绘，只在宽度变化超过阈值时重新布局
        if abs(current_width - self.last_window_width) > 50:
            new_layout_mode = self.determine_layout_mode(current_width)

            if new_layout_mode != self.current_layout_mode:
                # 保存当前状态
                self.save_current_state()

                # 重新创建布局
                self.root.after(100, self.create_responsive_layout)  # 延迟执行避免频繁重绘

            else:
                # 同一布局模式下，只更新数据列显示
                self.update_columns_layout()

            self.last_window_width = current_width

    def save_current_state(self):
        """保存当前界面状态"""
        # 这里可以保存用户的选择状态，在重新布局后恢复
        pass

    def update_columns_layout(self):
        """更新数据列布局"""
        if hasattr(self, 'columns_frame') and self.columns_frame.winfo_exists():
            # 根据当前布局模式调整每行显示的列数
            window_width = self.root.winfo_width()

            if self.current_layout_mode == 'narrow':
                new_columns_per_row = 2
            elif self.current_layout_mode == 'medium':
                new_columns_per_row = 3
            else:
                # 宽屏模式下根据实际宽度动态调整
                available_width = window_width - 500  # 减去控制面板宽度
                column_width = 200  # 每列大约需要的宽度
                new_columns_per_row = max(2, min(6, available_width // column_width))

            if new_columns_per_row != self.columns_per_row:
                self.columns_per_row = new_columns_per_row
                self.update_column_selection()  # 重新排列数据列选择

    def get_responsive_canvas_height(self):
        """根据布局模式获取Canvas高度"""
        window_height = self.root.winfo_height()

        if self.current_layout_mode == 'narrow':
            # 窄屏模式：较小高度，依赖滚动
            return min(200, window_height // 4)
        elif self.current_layout_mode == 'medium':
            # 中等屏幕：中等高度
            return min(280, window_height // 3)
        else:
            # 宽屏模式：较大高度
            return min(350, window_height // 2.5)

    def create_control_panel(self, parent):
        """创建左侧控制面板"""
        # 1. 数据源选择
        data_frame = ttk.LabelFrame(parent, text="1. 数据源选择", padding=10)
        data_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 文件选择
        ttk.Label(data_frame, text="选择数据文件:").pack(anchor=tk.W)
        file_frame = ttk.Frame(data_frame)
        file_frame.pack(fill=tk.X, pady=(5, 10))
        
        ttk.Entry(file_frame, textvariable=self.file_path_var, state='readonly').pack(side=tk.LEFT, fill=tk.X, expand=True)
        ttk.Button(file_frame, text="浏览", command=self.browse_file).pack(side=tk.RIGHT, padx=(5, 0))
        
        # 工作表选择
        ttk.Label(data_frame, text="工作表:").pack(anchor=tk.W)
        self.sheet_combo = ttk.Combobox(data_frame, textvariable=self.sheet_var, state='readonly')
        self.sheet_combo.pack(fill=tk.X, pady=(5, 0))
        self.sheet_combo.bind('<<ComboboxSelected>>', self.on_sheet_changed)
        
        # 2. 数据列映射
        mapping_frame = ttk.LabelFrame(parent, text="2. 数据列映射", padding=10)
        mapping_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 时间列选择
        ttk.Label(mapping_frame, text="时间列:").pack(anchor=tk.W)
        self.time_combo = ttk.Combobox(mapping_frame, textvariable=self.time_col_var, state='readonly')
        self.time_combo.pack(fill=tk.X, pady=(5, 10))
        self.time_combo.bind('<<ComboboxSelected>>', self.update_preview)
        
        # 数据列选择区域
        ttk.Label(mapping_frame, text="选择要绘制的数据列:").pack(anchor=tk.W)
        
        # 创建滚动框架
        canvas_frame = ttk.Frame(mapping_frame)
        canvas_frame.pack(fill=tk.BOTH, expand=True, pady=(5, 0))
        
        # 根据布局模式动态设置高度
        canvas_height = self.get_responsive_canvas_height()
        self.columns_canvas = tk.Canvas(canvas_frame, height=canvas_height)
        scrollbar = ttk.Scrollbar(canvas_frame, orient="vertical", command=self.columns_canvas.yview)
        self.columns_frame = ttk.Frame(self.columns_canvas)

        self.columns_canvas.configure(yscrollcommand=scrollbar.set)
        self.columns_canvas.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        self.columns_canvas.create_window((0, 0), window=self.columns_frame, anchor="nw")
        self.columns_frame.bind("<Configure>", lambda e: self.columns_canvas.configure(scrollregion=self.columns_canvas.bbox("all")))
        
        # 3. 图表配置
        config_frame = ttk.LabelFrame(parent, text="3. 图表配置", padding=10)
        config_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 图表标题
        ttk.Label(config_frame, text="图表标题:").pack(anchor=tk.W)
        title_entry = ttk.Entry(config_frame, textvariable=self.title_var)
        title_entry.pack(fill=tk.X, pady=(5, 10))
        title_entry.bind('<KeyRelease>', lambda e: self.update_preview())
        
        # Y轴标签
        ttk.Label(config_frame, text="Y轴标签:").pack(anchor=tk.W)
        ylabel_entry = ttk.Entry(config_frame, textvariable=self.ylabel_var)
        ylabel_entry.pack(fill=tk.X, pady=(5, 10))
        ylabel_entry.bind('<KeyRelease>', lambda e: self.update_preview())
        
        # 图表类型
        ttk.Label(config_frame, text="图表类型:").pack(anchor=tk.W)
        chart_type_combo = ttk.Combobox(config_frame, textvariable=self.chart_type_var, 
                                       values=self.chart_types, state='readonly')
        chart_type_combo.pack(fill=tk.X, pady=(5, 10))
        chart_type_combo.bind('<<ComboboxSelected>>', self.update_preview)
        
        # 颜色主题
        ttk.Label(config_frame, text="颜色主题:").pack(anchor=tk.W)
        theme_combo = ttk.Combobox(config_frame, textvariable=self.theme_var, 
                                  values=list(self.themes.keys()), state='readonly')
        theme_combo.pack(fill=tk.X, pady=(5, 10))
        theme_combo.bind('<<ComboboxSelected>>', self.update_preview)
        
        # 图表尺寸
        size_frame = ttk.Frame(config_frame)
        size_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(size_frame, text="宽度:").pack(side=tk.LEFT)
        width_entry = ttk.Entry(size_frame, textvariable=self.width_var, width=8)
        width_entry.pack(side=tk.LEFT, padx=(5, 10))
        width_entry.bind('<KeyRelease>', lambda e: self.update_preview())
        
        ttk.Label(size_frame, text="高度:").pack(side=tk.LEFT)
        height_entry = ttk.Entry(size_frame, textvariable=self.height_var, width=8)
        height_entry.pack(side=tk.LEFT, padx=(5, 0))
        height_entry.bind('<KeyRelease>', lambda e: self.update_preview())
        
        # 4. 操作按钮
        button_frame = ttk.LabelFrame(parent, text="4. 操作", padding=10)
        button_frame.pack(fill=tk.X)
        
        ttk.Button(button_frame, text="生成图表", command=self.generate_chart).pack(fill=tk.X, pady=(0, 5))
        ttk.Button(button_frame, text="保存图片", command=self.save_chart).pack(fill=tk.X, pady=(0, 5))
        ttk.Button(button_frame, text="全屏预览", command=self.show_fullscreen).pack(fill=tk.X, pady=(0, 5))
        ttk.Button(button_frame, text="转换为CSV", command=self.convert_current_file_to_csv).pack(fill=tk.X, pady=(0, 5))
        ttk.Button(button_frame, text="重置", command=self.reset_all).pack(fill=tk.X)
    
    def create_preview_area(self, parent):
        """创建响应式预览区域"""
        preview_frame = ttk.LabelFrame(parent, text="实时预览", padding=5)
        preview_frame.pack(fill=tk.BOTH, expand=True)

        # 根据布局模式调整图表尺寸
        fig_size = self.get_responsive_figure_size()

        # 创建matplotlib图形 - 优化显示设置
        plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
        plt.rcParams['axes.unicode_minus'] = False
        plt.rcParams['figure.constrained_layout.use'] = False  # 禁用约束布局，使用手动布局

        self.fig, self.ax = plt.subplots(figsize=fig_size, dpi=100)

        # 设置图表背景和边距
        self.fig.patch.set_facecolor('white')
        self.ax.set_facecolor('white')

        # 创建画布容器 - 增加更多边距
        canvas_container = ttk.Frame(preview_frame)
        canvas_container.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 创建画布
        self.canvas = FigureCanvasTkAgg(self.fig, canvas_container)
        canvas_widget = self.canvas.get_tk_widget()
        canvas_widget.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # 添加工具栏
        toolbar_frame = ttk.Frame(preview_frame)
        toolbar_frame.pack(side=tk.BOTTOM, fill=tk.X, pady=(5, 0))
        toolbar = NavigationToolbar2Tk(self.canvas, toolbar_frame)
        toolbar.update()

        # 优化初始图表布局 - 确保所有元素可见，特别是底部
        print("🔧 设置初始布局: bottom=0.60")
        self.fig.subplots_adjust(
            left=0.15,    # 左边距 - 为Y轴标签留空间
            bottom=0.60,  # 下边距 - 重构：增加到0.60确保X轴标签完全可见
            right=0.95,   # 右边距
            top=0.85,     # 上边距 - 为底部留出更多空间
            wspace=0.2,   # 子图间水平间距
            hspace=0.3    # 子图间垂直间距
        )

        # 绑定画布大小变化事件
        canvas_widget.bind('<Configure>', self.on_canvas_resize)

    def get_responsive_figure_size(self):
        """根据布局模式获取图表尺寸"""
        if self.current_layout_mode == 'narrow':
            return (8, 5)  # 窄屏：较小尺寸
        elif self.current_layout_mode == 'medium':
            return (8, 6)  # 中等屏幕：中等尺寸
        else:
            return (10, 7)  # 宽屏：较大尺寸

    def on_canvas_resize(self, event):
        """画布大小变化事件处理 - 优化版本"""
        if hasattr(self, 'fig') and self.fig and hasattr(self, 'canvas'):
            try:
                # 获取新的画布尺寸
                width_px = event.width
                height_px = event.height

                # 确保尺寸合理
                if width_px < 200 or height_px < 150:
                    return

                # 转换为英寸（matplotlib使用英寸）
                dpi = self.fig.get_dpi()
                width_inch = max(3, (width_px - 20) / dpi)  # 减去边距，最小3英寸
                height_inch = max(2, (height_px - 20) / dpi)  # 减去边距，最小2英寸

                # 更新图表尺寸
                self.fig.set_size_inches(width_inch, height_inch, forward=True)

                # 根据新尺寸重新调整布局
                self.smart_layout_adjustment(width_inch, height_inch)

                # 重新绘制
                self.canvas.draw_idle()

            except Exception as e:
                print(f"画布调整大小失败: {e}")

    def smart_layout_adjustment(self, width_inch, height_inch):
        """智能布局调整 - 确保底部边距足够 - 已修复版本"""
        print("🔥🔥🔥 新版本智能布局调整方法被调用 🔥🔥🔥")
        print(f"🔥 输入参数: width_inch={width_inch}, height_inch={height_inch}")
        try:
            # 根据图表尺寸动态调整边距
            if width_inch < 5:  # 窄图表
                left_margin = 0.20
                right_margin = 0.90
            elif width_inch < 8:  # 中等宽度
                left_margin = 0.15
                right_margin = 0.92
            else:  # 宽图表
                left_margin = 0.12
                right_margin = 0.95

            # 重构：统一底部边距为0.60，确保X轴标签完全可见
            bottom_margin = 0.60  # 统一使用大的底部边距
            if height_inch < 3:  # 矮图表
                top_margin = 0.65  # 为底部留出更多空间
                print(f"🔧 矮图表分支: height_inch={height_inch} < 3, bottom_margin={bottom_margin}")
            elif height_inch < 5:  # 中等高度
                top_margin = 0.75
                print(f"🔧 中等高度分支: 3 <= height_inch={height_inch} < 5, bottom_margin={bottom_margin}")
            else:  # 高图表
                top_margin = 0.80
                print(f"🔧 高图表分支: height_inch={height_inch} >= 5, bottom_margin={bottom_margin}")

            # 应用调整
            self.fig.subplots_adjust(
                left=left_margin,
                bottom=bottom_margin,
                right=right_margin,
                top=top_margin,
                wspace=0.2,
                hspace=0.3
            )

            print(f"🔥 新版本智能布局调整: 尺寸={width_inch:.1f}x{height_inch:.1f}, bottom={bottom_margin}")

        except Exception as e:
            print(f"智能布局调整失败: {e}")
            # 重构：使用统一的大底部边距
            print("🔧 智能布局异常，使用安全默认值: bottom=0.60")
            self.fig.subplots_adjust(left=0.15, bottom=0.60, right=0.95, top=0.80)
        
        # 初始化空图表
        self.ax.text(0.5, 0.5, '请选择数据文件开始', 
                    horizontalalignment='center', verticalalignment='center',
                    transform=self.ax.transAxes, fontsize=16)
        self.ax.set_xticks([])
        self.ax.set_yticks([])
        self.canvas.draw()

    def fix_chart_display(self):
        """修复图表显示问题 - 增强版本"""
        if hasattr(self, 'fig') and self.fig and hasattr(self, 'canvas'):
            try:
                # 获取当前画布尺寸
                canvas_widget = self.canvas.get_tk_widget()
                canvas_widget.update_idletasks()

                width = canvas_widget.winfo_width()
                height = canvas_widget.winfo_height()

                if width > 100 and height > 100:
                    # 转换为英寸，留出边距
                    dpi = self.fig.get_dpi()
                    width_inch = max(3, (width - 30) / dpi)  # 减去30px边距
                    height_inch = max(2, (height - 30) / dpi)  # 减去30px边距

                    # 设置图表尺寸
                    self.fig.set_size_inches(width_inch, height_inch, forward=True)

                    # 检查是否有数据被绘制
                    has_data = len(self.ax.lines) > 0 or len(self.ax.collections) > 0

                    if has_data:
                        # 有数据时使用智能布局
                        self.apply_smart_layout()
                    else:
                        # 重构：无数据时也使用统一的大底部边距
                        print("🔧 无数据状态，设置布局: bottom=0.60")
                        self.fig.subplots_adjust(left=0.15, bottom=0.60, right=0.95, top=0.80)

                    # 确保所有文本元素可见
                    self.ensure_text_visibility()

                    # 重新绘制
                    self.canvas.draw_idle()

            except Exception as e:
                print(f"修复图表显示失败: {e}")

    def apply_smart_layout(self):
        """应用智能布局 - 确保底部边距足够"""
        try:
            # 检查图例是否存在
            legend = self.ax.get_legend()
            has_legend = legend is not None

            # 检查轴标签长度
            xlabel_len = len(self.ax.get_xlabel())
            ylabel_len = len(self.ax.get_ylabel())
            title_len = len(self.ax.get_title())

            # 重构：统一使用大的底部边距，确保X轴标签完全可见
            left_margin = 0.15 if ylabel_len > 10 else 0.12
            bottom_margin = 0.60  # 统一使用0.60的大底部边距
            right_margin = 0.85 if has_legend and self.current_layout_mode == 'wide' else 0.95
            top_margin = 0.80  # 为底部留出更多空间

            # 应用布局
            self.fig.subplots_adjust(
                left=left_margin,
                bottom=bottom_margin,
                right=right_margin,
                top=top_margin,
                wspace=0.2,
                hspace=0.3
            )

            print(f"🔧 应用智能布局: xlabel_len={xlabel_len}, bottom_margin={bottom_margin}")

        except Exception as e:
            print(f"智能布局应用失败: {e}")
            # 重构：使用统一的大底部边距
            print("🔧 智能布局应用失败，使用安全默认值: bottom=0.60")
            self.fig.subplots_adjust(left=0.15, bottom=0.60, right=0.95, top=0.80)

    def ensure_text_visibility(self):
        """确保所有文本元素可见"""
        try:
            # 调整刻度标签
            self.ax.tick_params(axis='both', which='major', labelsize=9)
            self.ax.tick_params(axis='both', which='minor', labelsize=8)

            # 确保X轴标签不重叠
            plt.setp(self.ax.xaxis.get_majorticklabels(), rotation=0)

            # 如果标签太多，进行旋转
            if len(self.ax.get_xticklabels()) > 10:
                plt.setp(self.ax.xaxis.get_majorticklabels(), rotation=45, ha='right')

        except Exception as e:
            print(f"文本可见性调整失败: {e}")

    def get_best_legend_location(self):
        """根据当前布局模式确定最佳图例位置"""
        if hasattr(self, 'current_layout_mode'):
            if self.current_layout_mode == 'narrow':
                return 'upper right'  # 窄屏模式：右上角
            elif self.current_layout_mode == 'medium':
                return 'best'  # 中等屏幕：自动选择
            else:
                return 'center left'  # 宽屏模式：左侧外部
        return 'best'

    def adjust_layout_for_content(self, selected_columns, time_col):
        """根据内容动态调整布局参数"""
        try:
            # 重构：统一使用大的底部边距
            left_margin = 0.15
            bottom_margin = 0.60  # 重构：统一使用0.60的大底部边距
            right_margin = 0.95
            top_margin = 0.80     # 为底部留出更多空间
            print(f"🔧 内容布局调整: bottom_margin={bottom_margin}")

            # 根据Y轴标签长度调整左边距
            ylabel_text = self.ylabel_var.get() or "数值"
            if len(ylabel_text) > 10:
                left_margin = 0.18

            # 根据标题长度调整上边距
            title_text = self.title_var.get() or "数据对比图"
            if len(title_text) > 20:
                top_margin = 0.88

            # 重构：时间轴和图例都使用统一的大底部边距
            if time_col:
                print("🔧 检测到时间列，保持bottom_margin=0.60")
                # bottom_margin已经是0.60，无需调整

            # 根据图例数量调整边距
            if selected_columns:
                if len(selected_columns) > 5:
                    if self.current_layout_mode == 'wide':
                        right_margin = 0.85  # 为外部图例留空间
                    else:
                        print("🔧 多列数据，保持bottom_margin=0.60")
                        # bottom_margin已经是0.60，无需调整

            # 应用调整后的布局参数
            self.fig.subplots_adjust(
                left=left_margin,
                bottom=bottom_margin,
                right=right_margin,
                top=top_margin,
                wspace=0.2,
                hspace=0.3
            )

        except Exception as e:
            print(f"布局调整失败: {e}")
            # 重构：使用统一的大底部边距
            print("🔧 内容布局调整失败，使用安全默认值: bottom=0.60")
            self.fig.subplots_adjust(left=0.15, bottom=0.60, right=0.95, top=0.80)

    def ensure_bottom_margin(self):
        """确保底部边距足够显示X轴标签"""
        try:
            # 获取当前子图位置
            pos = self.ax.get_position()

            # 重构：确保底部边距至少为0.60
            min_bottom = 0.60

            # 无论当前状态如何，都强制应用大的底部边距
            self.fig.subplots_adjust(
                left=0.15,      # 固定左边距
                bottom=min_bottom,  # 强制大底部边距
                right=0.95,     # 固定右边距
                top=0.80,       # 为底部留出更多空间
                wspace=0.2,
                hspace=0.3
            )

            print(f"🔧 强制调整底部边距到: {min_bottom}")

        except Exception as e:
            print(f"底部边距调整失败: {e}")
            # 重构：使用统一的大底部边距
            print("🔧 底部边距调整失败，使用安全默认值: bottom=0.60")
            self.fig.subplots_adjust(left=0.15, bottom=0.60, right=0.95, top=0.80)

    def force_safe_layout(self):
        """强制应用安全的布局参数，确保图表完整显示"""
        try:
            # 重构：无论当前状态如何，都强制应用大的底部边距
            self.fig.subplots_adjust(
                left=0.15,    # 左边距：为Y轴标签留空间
                bottom=0.60,  # 重构：底部边距增加到0.60确保X轴标签完全可见
                right=0.95,   # 右边距：为图表右侧留空间
                top=0.80,     # 上边距：为底部留出更多空间
                wspace=0.2,   # 子图间水平间距
                hspace=0.3    # 子图间垂直间距
            )
            print("🔧 强制应用安全布局: bottom=0.60")

        except Exception as e:
            print(f"强制布局设置失败: {e}")

    def final_layout_check(self):
        """最终布局检查，确保图表显示完整"""
        try:
            # 获取当前子图位置
            pos = self.ax.get_position()

            # 重构：如果底部边距小于0.55，强制修复
            if pos.y0 < 0.55:
                print(f"🔧 检测到底部边距不足: {pos.y0}, 强制修复到0.60")
                self.fig.subplots_adjust(
                    left=0.15,
                    bottom=0.60,
                    right=0.95,
                    top=0.80,
                    wspace=0.2,
                    hspace=0.3
                )
            else:
                print(f"🔧 底部边距检查通过: {pos.y0}")

        except Exception as e:
            print(f"最终布局检查失败: {e}")
            # 重构：最后的安全措施，使用大底部边距
            print("🔧 最终布局检查失败，使用安全默认值: bottom=0.60")
            self.fig.subplots_adjust(left=0.15, bottom=0.60, right=0.95, top=0.80)

    def ultimate_layout_fix(self):
        """终极布局修复 - 最简单直接的方法"""
        try:
            print("🔥 执行终极布局修复...")
            # 重构：直接强制设置大的底部边距
            self.fig.subplots_adjust(
                left=0.15,
                bottom=0.60,  # 重构：使用统一的大底部边距
                right=0.95,
                top=0.80,     # 为底部留出更多空间
                wspace=0.2,
                hspace=0.3
            )

            # 验证设置是否生效
            pos = self.ax.get_position()
            print(f"🔥 终极修复后的底部边距: {pos.y0}")

            # 如果还是不够，再次强制设置
            if pos.y0 < 0.55:
                print("🔥 第一次修复不够，再次强制设置...")
                self.fig.subplots_adjust(left=0.10, bottom=0.65, right=0.95, top=0.75)
                pos = self.ax.get_position()
                print(f"🔥 二次修复后的底部边距: {pos.y0}")

        except Exception as e:
            print(f"终极布局修复失败: {e}")

    def final_force_layout_fix(self):
        """最终强制布局修复 - 在所有其他操作后执行"""
        try:
            print("🔥 执行最终强制布局修复...")

            # 重构：直接强制设置统一的大底部边距
            self.fig.subplots_adjust(
                left=0.12,
                bottom=0.60,  # 重构：使用统一的大底部边距
                right=0.95,
                top=0.80,
                wspace=0.2,
                hspace=0.3
            )

            # 强制重绘
            self.canvas.draw_idle()

            # 验证结果
            pos = self.ax.get_position()
            print(f"🔥 最终修复后的底部边距: {pos.y0}")

            if pos.y0 < 0.55:
                print("🔥 第一次修复不够，再次强制设置...")
                self.fig.subplots_adjust(left=0.10, bottom=0.65, right=0.95, top=0.75)
                self.canvas.draw_idle()
                pos = self.ax.get_position()
                print(f"🔥 二次修复后的底部边距: {pos.y0}")

        except Exception as e:
            print(f"最终强制布局修复失败: {e}")

    def create_status_bar(self, parent):
        """创建状态栏"""
        status_frame = ttk.Frame(parent)
        status_frame.pack(fill=tk.X, side=tk.BOTTOM, pady=(10, 0))

        # 状态标签
        ttk.Label(status_frame, text="状态:").pack(side=tk.LEFT)
        status_label = ttk.Label(status_frame, textvariable=self.status_var,
                               foreground="blue", font=("Arial", 9))
        status_label.pack(side=tk.LEFT, padx=(5, 0))

        # 分隔符
        ttk.Separator(status_frame, orient='vertical').pack(side=tk.LEFT, fill=tk.Y, padx=(20, 10))

        # 帮助按钮
        ttk.Button(status_frame, text="Excel兼容性帮助",
                  command=self.show_compatibility_help).pack(side=tk.RIGHT)
    
    def browse_file(self):
        """浏览文件"""
        file_path = filedialog.askopenfilename(
            title="选择数据文件",
            filetypes=[
                ("所有支持格式", "*.csv;*.xlsx;*.xls"),
                ("CSV文件", "*.csv"),
                ("Excel文件", "*.xlsx;*.xls"),
                ("所有文件", "*.*")
            ]
        )
        
        if file_path:
            self.file_path = file_path
            self.file_path_var.set(os.path.basename(file_path))  # 只显示文件名
            self.load_file()
    
    def load_file(self):
        """加载文件"""
        try:
            self.status_var.set("正在加载文件...")
            self.root.update()

            file_ext = os.path.splitext(self.file_path)[1].lower()

            if file_ext == '.csv':
                self.status_var.set("正在读取CSV文件...")
                self.root.update()

                # 加载CSV文件
                for encoding in ['utf-8', 'gbk', 'gb2312', 'utf-8-sig']:
                    try:
                        # 使用更安全的参数加载CSV
                        self.data = pd.read_csv(self.file_path, encoding=encoding,
                                              keep_default_na=True, na_values=['', 'NA', 'N/A', 'null'])
                        break
                    except UnicodeDecodeError:
                        continue
                    except Exception as e:
                        print(f"CSV加载错误 ({encoding}): {e}")
                        continue
                else:
                    raise ValueError("无法识别文件编码")

                self.sheet_names = ["CSV数据"]
                self.sheet_combo['values'] = self.sheet_names
                self.sheet_var.set(self.sheet_names[0])

            elif file_ext in ['.xlsx', '.xls']:
                self.status_var.set("正在读取Excel文件（尝试兼容性处理）...")
                self.root.update()

                # 加载Excel文件 - 使用改进的方法处理兼容性问题
                self.data, self.sheet_names = self.load_excel_safe(self.file_path)
                self.sheet_combo['values'] = self.sheet_names
                self.sheet_var.set(self.sheet_names[0])
                print(f"Excel工作表: {self.sheet_names}")
                print(f"Excel数据加载成功: {self.data.shape}")
            else:
                raise ValueError(f"不支持的文件格式: {file_ext}")

            # 数据清理和验证
            if self.data is None or self.data.empty:
                raise ValueError("文件为空或无法读取数据")

            print(f"原始数据形状: {self.data.shape}")
            print(f"列名: {list(self.data.columns)}")

            self.status_var.set("正在处理数据...")
            self.root.update()

            self.process_data()

            self.status_var.set(f"文件加载成功！数据形状: {self.data.shape}")
            messagebox.showinfo("成功", f"文件加载成功！\n数据形状: {self.data.shape}")

        except Exception as e:
            print(f"文件加载详细错误: {e}")
            import traceback
            traceback.print_exc()
            self.status_var.set("文件加载失败")
            self.show_excel_error_dialog(str(e))

    def load_excel_safe(self, file_path):
        """
        安全加载Excel文件，处理兼容性问题
        返回: (DataFrame, sheet_names)
        """
        print(f"正在尝试安全加载Excel文件: {file_path}")

        # 首先获取工作表名称
        sheet_names = self.get_sheet_names_safe(file_path)

        # 扩展的方法列表，按优先级排序
        methods = [
            self._excel_method_standard,
            self._excel_method_data_only,
            self._excel_method_read_only,
            self._excel_method_manual_extract,
            self._excel_method_csv_convert,
            self._excel_method_repair_retry,
        ]

        last_error = None
        for i, method in enumerate(methods, 1):
            try:
                print(f"方法{i}: {method.__name__}...")
                df = method(file_path, sheet_names[0])
                if df is not None and not df.empty:
                    print(f"✓ 方法{i}成功！数据形状: {df.shape}")
                    return df, sheet_names
            except Exception as e:
                print(f"✗ 方法{i}失败: {e}")
                last_error = e
                continue

        # 所有方法都失败，提供详细的错误信息和解决建议
        error_msg = f"无法读取Excel文件: {os.path.basename(file_path)}\n\n"
        error_msg += f"最后错误: {last_error}\n\n"
        error_msg += "建议的解决方案:\n"
        error_msg += "1. 用Excel重新打开文件，另存为新的.xlsx文件\n"
        error_msg += "2. 将Excel文件导出为CSV格式\n"
        error_msg += "3. 检查文件是否损坏或包含不兼容的格式\n"
        error_msg += "4. 尝试删除文件中的复杂样式和格式\n"
        error_msg += "5. 使用'选择性粘贴'只保留数值到新文件"

        raise Exception(error_msg)

    def _excel_method_standard(self, file_path, sheet_name):
        """方法1: 标准pandas读取"""
        return pd.read_excel(file_path,
                           sheet_name=sheet_name,
                           keep_default_na=True,
                           na_values=['', 'NA', 'N/A', 'null', '#N/A'])

    def _excel_method_data_only(self, file_path, sheet_name):
        """方法2: 使用data_only=True忽略公式和样式"""
        return pd.read_excel(file_path,
                           sheet_name=sheet_name,
                           engine='openpyxl',
                           engine_kwargs={'data_only': True},
                           keep_default_na=True,
                           na_values=['', 'NA', 'N/A', 'null', '#N/A'])

    def _excel_method_read_only(self, file_path, sheet_name):
        """方法3: 使用只读模式"""
        return pd.read_excel(file_path,
                           sheet_name=sheet_name,
                           engine='openpyxl',
                           engine_kwargs={'data_only': True, 'read_only': True},
                           keep_default_na=True,
                           na_values=['', 'NA', 'N/A', 'null', '#N/A'])

    def _excel_method_manual_extract(self, file_path, sheet_name):
        """方法4: 手动提取数据，完全忽略样式"""
        import openpyxl

        # 尝试不同的加载参数
        load_params = [
            {'data_only': True, 'read_only': True, 'keep_vba': False},
            {'data_only': True, 'read_only': False, 'keep_vba': False},
            {'read_only': True, 'keep_vba': False},
            {'keep_vba': False},
            {}
        ]

        wb = None
        for params in load_params:
            try:
                wb = openpyxl.load_workbook(file_path, **params)
                break
            except Exception as e:
                print(f"    加载参数 {params} 失败: {e}")
                continue

        if wb is None:
            raise Exception("无法加载工作簿")

        # 获取工作表
        if isinstance(sheet_name, str):
            ws = wb[sheet_name]
        else:
            ws = wb.worksheets[sheet_name]

        # 手动提取数据
        data = []
        max_row = ws.max_row
        max_col = ws.max_column

        print(f"    工作表尺寸: {max_row} x {max_col}")

        for row in range(1, min(max_row + 1, 10000)):  # 限制最大行数
            row_data = []
            has_data = False
            for col in range(1, min(max_col + 1, 100)):  # 限制最大列数
                try:
                    cell = ws.cell(row=row, column=col)
                    value = cell.value
                    if value is not None:
                        has_data = True
                    row_data.append(value)
                except Exception:
                    row_data.append(None)

            if has_data:
                data.append(row_data)
            elif data:  # 如果已经有数据了，遇到连续空行就停止
                break

        if not data:
            raise ValueError("工作表为空")

        # 处理列名
        columns = [str(col) if col is not None else f'Column_{i}'
                  for i, col in enumerate(data[0])]
        df_data = data[1:] if len(data) > 1 else []
        df = pd.DataFrame(df_data, columns=columns)

        # 清理空列
        df = df.dropna(axis=1, how='all')

        return df

    def _excel_method_csv_convert(self, file_path, sheet_name):
        """方法5: 转换为CSV再读取"""
        import openpyxl
        import tempfile
        import csv

        # 创建临时目录
        temp_dir = tempfile.mkdtemp()
        csv_file = os.path.join(temp_dir, 'temp_excel_data.csv')

        try:
            # 尝试加载工作簿
            wb = openpyxl.load_workbook(file_path, data_only=True)
            if isinstance(sheet_name, str):
                ws = wb[sheet_name]
            else:
                ws = wb.worksheets[sheet_name]

            # 写入CSV
            with open(csv_file, 'w', encoding='utf-8', newline='') as f:
                writer = csv.writer(f)
                for row in ws.iter_rows(values_only=True):
                    if any(cell is not None for cell in row):
                        clean_row = [str(cell) if cell is not None else '' for cell in row]
                        writer.writerow(clean_row)

            # 读取CSV
            df = pd.read_csv(csv_file, encoding='utf-8')
            return df

        finally:
            # 清理临时文件
            try:
                import shutil
                shutil.rmtree(temp_dir)
            except:
                pass

    def _excel_method_repair_retry(self, file_path, sheet_name):
        """方法6: 尝试修复后重试"""
        import tempfile
        import shutil

        # 创建临时目录
        temp_dir = tempfile.mkdtemp()
        temp_file = os.path.join(temp_dir, 'repaired_' + os.path.basename(file_path))

        try:
            # 复制文件到临时位置
            shutil.copy2(file_path, temp_file)

            # 尝试用标准方法读取临时文件
            df = pd.read_excel(temp_file,
                             sheet_name=sheet_name,
                             engine='openpyxl',
                             engine_kwargs={'data_only': True})
            return df

        finally:
            # 清理临时文件
            try:
                shutil.rmtree(temp_dir)
            except:
                pass

    def get_sheet_names_safe(self, file_path):
        """安全获取Excel文件的工作表名称"""
        try:
            import openpyxl
            wb = openpyxl.load_workbook(file_path, read_only=True)
            return wb.sheetnames
        except:
            try:
                excel_file = pd.ExcelFile(file_path)
                return excel_file.sheet_names
            except:
                return ["Sheet1"]  # 默认工作表名
    
    def on_sheet_changed(self, event=None):
        """工作表改变时的处理"""
        if self.file_path and self.sheet_var.get():
            try:
                print(f"切换到工作表: {self.sheet_var.get()}")

                # 使用安全的Excel加载方法
                self.data = self.load_excel_sheet_safe(self.file_path, self.sheet_var.get())

                print(f"工作表数据形状: {self.data.shape}")
                self.process_data()
            except Exception as e:
                print(f"工作表加载错误: {e}")
                import traceback
                traceback.print_exc()
                self.show_excel_error_dialog(f"工作表切换失败: {str(e)}")

    def load_excel_sheet_safe(self, file_path, sheet_name):
        """安全加载指定的Excel工作表"""
        print(f"正在加载工作表: {sheet_name}")

        # 方法1: 使用data_only=True
        try:
            df = pd.read_excel(file_path,
                              sheet_name=sheet_name,
                              engine='openpyxl',
                              engine_kwargs={'data_only': True},
                              keep_default_na=True,
                              na_values=['', 'NA', 'N/A', 'null', '#N/A'])
            print(f"✓ 工作表加载成功！数据形状: {df.shape}")
            return df
        except Exception as e:
            print(f"✗ 标准方法失败: {e}")

        # 方法2: 手动提取
        try:
            import openpyxl
            wb = openpyxl.load_workbook(file_path, data_only=True, read_only=True)
            ws = wb[sheet_name]

            data = []
            for row in ws.iter_rows(values_only=True):
                if any(cell is not None for cell in row):
                    data.append(row)

            if data:
                columns = [str(col) if col is not None else f'Column_{i}' for i, col in enumerate(data[0])]
                df = pd.DataFrame(data[1:], columns=columns)
                print(f"✓ 手动提取成功！数据形状: {df.shape}")
                return df
            else:
                raise ValueError("工作表为空")

        except Exception as e:
            print(f"✗ 手动提取失败: {e}")
            raise Exception(f"无法加载工作表 {sheet_name}")
    
    def process_data(self):
        """处理数据"""
        if self.data is None:
            return

        try:
            print("开始处理数据...")

            # 清理列名 - 确保列名是字符串
            self.data.columns = [str(col).strip() for col in self.data.columns]

            # 删除完全空的行和列
            original_shape = self.data.shape
            self.data = self.data.dropna(how='all').dropna(axis=1, how='all')
            print(f"数据清理: {original_shape} -> {self.data.shape}")

            # 处理数据类型 - 尝试转换数值列
            for col in self.data.columns:
                try:
                    # 检查列的数据类型
                    col_dtype = self.data[col].dtype
                    print(f"列 '{col}' 原始类型: {col_dtype}")

                    if col_dtype == 'object':
                        # 尝试转换为数值类型
                        try:
                            # 先清理数据，处理各种可能的值
                            cleaned_data = self.data[col].copy()

                            # 将非字符串类型转换为字符串，但保留NaN
                            mask_notna = cleaned_data.notna()
                            cleaned_data[mask_notna] = cleaned_data[mask_notna].astype(str).str.strip()

                            # 尝试转换为数值
                            numeric_data = pd.to_numeric(cleaned_data, errors='coerce')

                            # 如果转换成功的比例超过50%，则使用数值类型
                            valid_ratio = numeric_data.notna().sum() / len(numeric_data)
                            if valid_ratio > 0.5:
                                self.data[col] = numeric_data
                                print(f"列 '{col}' 转换为数值类型 (成功率: {valid_ratio:.2%})")
                            else:
                                print(f"列 '{col}' 保持为文本类型 (数值转换率: {valid_ratio:.2%})")
                        except Exception as e:
                            print(f"列 '{col}' 数值转换失败: {e}")

                    elif 'datetime' in str(col_dtype).lower():
                        print(f"列 '{col}' 是时间类型")
                    elif 'float' in str(col_dtype).lower() or 'int' in str(col_dtype).lower():
                        print(f"列 '{col}' 是数值类型")
                    else:
                        print(f"列 '{col}' 类型: {col_dtype}")

                except Exception as e:
                    print(f"处理列 '{col}' 时出错: {e}")
                    continue

            print("数据处理完成")

            # 更新列选择
            self.update_column_selection()

            # 自动检测时间列
            time_col = self.detect_time_column()
            if time_col:
                self.time_col_var.set(time_col)
                print(f"检测到时间列: {time_col}")

            # 更新预览
            self.update_preview()

        except Exception as e:
            print(f"数据处理错误: {e}")
            import traceback
            traceback.print_exc()
            messagebox.showerror("数据处理错误", f"数据处理失败: {str(e)}")
    
    def detect_time_column(self):
        """自动检测时间列"""
        if self.data is None or self.data.empty:
            return None

        try:
            time_keywords = ['时间', 'time', '日期', 'date', 'datetime', '时刻']

            # 检查列名
            for col in self.data.columns:
                col_str = str(col).lower()
                if any(keyword in col_str for keyword in time_keywords):
                    return col

            # 检查第一列内容
            if len(self.data.columns) > 0:
                first_col = self.data.columns[0]
                sample_data = self.data[first_col].dropna().head(3)

                for value in sample_data:
                    try:
                        value_str = str(value)
                        # 检查是否包含时间格式的字符
                        if any(char in value_str for char in [':', '-', '/', ' ']):
                            # 进一步验证是否可能是时间格式
                            if len(value_str) >= 8:  # 最短的时间格式如 "12:34:56"
                                return first_col
                    except Exception:
                        continue

            return None

        except Exception as e:
            print(f"时间列检测错误: {e}")
            return None

    def update_column_selection(self):
        """更新数据列选择界面"""
        try:
            # 清除现有组件
            for widget in self.columns_frame.winfo_children():
                widget.destroy()

            self.column_vars.clear()
            self.label_vars.clear()
            self.color_vars.clear()

            if self.data is None or self.data.empty:
                return

            # 更新时间列选择
            columns = [str(col) for col in self.data.columns]  # 确保列名是字符串
            self.time_combo['values'] = columns

            # 创建数据列选择 - 响应式多行多列网格布局
            columns_per_row = self.columns_per_row  # 使用响应式列数

            for i, col in enumerate(columns):
                try:
                    # 计算行列位置
                    row = i // columns_per_row
                    col_pos = i % columns_per_row

                    # 创建主框架（每个数据列一个）- 更紧凑的设计
                    main_frame = ttk.LabelFrame(self.columns_frame, text=f"列{i+1}", padding=3)
                    main_frame.grid(row=row, column=col_pos, padx=3, pady=3, sticky="ew")

                    # 配置网格权重
                    self.columns_frame.grid_columnconfigure(col_pos, weight=1)

                    # 复选框和列名显示 - 紧凑布局
                    checkbox_frame = ttk.Frame(main_frame)
                    checkbox_frame.pack(fill=tk.X, pady=(0, 2))

                    var = tk.BooleanVar()
                    self.column_vars[col] = var
                    checkbox = ttk.Checkbutton(checkbox_frame, variable=var, command=self.update_preview)
                    checkbox.pack(side=tk.LEFT)

                    # 原始列名标签 - 紧凑显示
                    display_name = str(col)
                    full_name = display_name  # 保存完整名称用于工具提示
                    # 如果列名太长，智能截断并显示省略号
                    if len(display_name) > 8:
                        display_name = display_name[:8] + "..."

                    col_label = ttk.Label(checkbox_frame, text=display_name,
                                         font=("Arial", 8), justify=tk.LEFT)
                    col_label.pack(side=tk.LEFT, padx=(2, 0))

                    # 添加工具提示显示完整列名
                    self.create_tooltip(col_label, full_name)

                    # 自定义标签输入框 - 紧凑布局
                    label_var = tk.StringVar(value=str(col))
                    self.label_vars[col] = label_var
                    entry = ttk.Entry(main_frame, textvariable=label_var, font=("Arial", 8), width=12)
                    entry.pack(fill=tk.X, pady=(0, 2))
                    entry.bind('<KeyRelease>', lambda e, col=col: self.update_preview())

                    # 颜色选择 - 紧凑布局
                    color_frame = ttk.Frame(main_frame)
                    color_frame.pack(fill=tk.X)

                    ttk.Label(color_frame, text="颜色:", font=("Arial", 7)).pack(side=tk.LEFT)

                    color = self.colors[i % len(self.colors)]
                    self.color_vars[col] = color
                    color_button = tk.Button(color_frame, text="●", bg=color, width=3, height=1,
                                           font=("Arial", 10), relief=tk.RAISED,
                                           command=lambda c=col: self.choose_color(c))
                    color_button.pack(side=tk.RIGHT)

                except Exception as e:
                    print(f"创建列选择组件失败 (列: {col}): {e}")
                    continue

            # 更新滚动区域
            self.columns_frame.update_idletasks()
            self.columns_canvas.configure(scrollregion=self.columns_canvas.bbox("all"))

        except Exception as e:
            print(f"更新列选择界面失败: {e}")
            import traceback
            traceback.print_exc()

    def choose_color(self, column):
        """选择颜色"""
        color = colorchooser.askcolor(color=self.color_vars[column])[1]
        if color:
            self.color_vars[column] = color
            # 更新按钮颜色
            for widget in self.columns_frame.winfo_children():
                if isinstance(widget, ttk.Frame):
                    for child in widget.winfo_children():
                        if isinstance(child, tk.Button) and child.cget('text') == '●':
                            child.config(bg=color)
                            break
            self.update_preview()

    def update_preview(self, event=None):
        """更新预览图表"""
        if self.data is None:
            return

        try:
            # 清除当前图表
            self.ax.clear()

            # 获取选中的数据列
            selected_columns = [col for col, var in self.column_vars.items() if var.get()]

            if not selected_columns:
                self.ax.text(0.5, 0.5, '请选择要绘制的数据列',
                           horizontalalignment='center', verticalalignment='center',
                           transform=self.ax.transAxes, fontsize=14)
                self.canvas.draw()
                return

            # 获取时间列
            time_col = self.time_col_var.get()

            # 准备数据
            plot_data = self.data.copy()

            # 转换时间列
            if time_col and time_col in plot_data.columns:
                try:
                    plot_data[time_col] = pd.to_datetime(plot_data[time_col])
                    x_data = plot_data[time_col]
                except:
                    x_data = range(len(plot_data))
            else:
                x_data = range(len(plot_data))

            # 获取颜色主题
            theme = self.themes.get(self.theme_var.get(), self.themes['默认'])
            theme_colors = theme['colors']

            # 绘制曲线
            chart_type = self.chart_type_var.get()

            for i, col in enumerate(selected_columns):
                if col in plot_data.columns:
                    # 使用自定义颜色或主题颜色
                    color = self.color_vars.get(col, theme_colors[i % len(theme_colors)])
                    label = self.label_vars.get(col, tk.StringVar(value=col)).get()

                    # 转换数值数据
                    y_data = pd.to_numeric(plot_data[col], errors='coerce').dropna()
                    x_data_clean = x_data[:len(y_data)]

                    # 根据图表类型绘制
                    if chart_type == '线图':
                        self.ax.plot(x_data_clean, y_data,
                                   color=color,
                                   linewidth=1.5,
                                   label=label,
                                   alpha=0.8,
                                   marker='o' if len(y_data) < 50 else '',
                                   markersize=3)

                    elif chart_type == '散点图':
                        self.ax.scatter(x_data_clean, y_data,
                                      color=color,
                                      label=label,
                                      alpha=0.7,
                                      s=20)

                    elif chart_type == '柱状图':
                        self.ax.bar(x_data_clean, y_data,
                                  color=color,
                                  label=label,
                                  alpha=0.7,
                                  width=0.8)

                    elif chart_type == '面积图':
                        self.ax.fill_between(x_data_clean, y_data,
                                           color=color,
                                           label=label,
                                           alpha=0.5)

            # 设置图表样式 - 优化字体大小和位置
            title_text = self.title_var.get() or "数据对比图"
            self.ax.set_title(title_text, fontsize=14, fontweight='bold', pad=20)

            ylabel_text = self.ylabel_var.get() or "数值"
            self.ax.set_ylabel(ylabel_text, fontsize=11, labelpad=10)

            xlabel_text = '时间' if time_col else '数据点'
            self.ax.set_xlabel(xlabel_text, fontsize=11, labelpad=10)

            # 添加网格
            self.ax.grid(True, alpha=0.3, linestyle='--', linewidth=0.5)

            # 优化图例位置和样式
            if selected_columns:
                # 计算最佳图例位置
                legend_loc = self.get_best_legend_location()
                legend = self.ax.legend(
                    loc=legend_loc,
                    fontsize=9,
                    frameon=True,
                    fancybox=True,
                    shadow=True,
                    framealpha=0.9,
                    bbox_to_anchor=(1.02, 1) if legend_loc == 'center left' else None
                )
                legend.get_frame().set_facecolor('white')

            # 格式化时间轴
            if time_col and pd.api.types.is_datetime64_any_dtype(plot_data[time_col]):
                self.ax.xaxis.set_major_formatter(mdates.DateFormatter('%H:%M'))
                # 旋转标签并调整位置
                for label in self.ax.get_xticklabels():
                    label.set_rotation(45)
                    label.set_horizontalalignment('right')

            # 美化图表
            self.ax.spines['top'].set_visible(False)
            self.ax.spines['right'].set_visible(False)
            self.ax.spines['left'].set_linewidth(0.5)
            self.ax.spines['bottom'].set_linewidth(0.5)

            # 设置数据边距
            self.ax.margins(x=0.02, y=0.05)

            # 动态调整布局参数
            self.adjust_layout_for_content(selected_columns, time_col)

            # 强制确保底部边距足够 - 修复显示不全问题
            self.ensure_bottom_margin()

            # 最终强制设置安全的布局参数
            self.force_safe_layout()

            # 额外的安全检查 - 确保底部边距绝对足够
            self.final_layout_check()

            # 终极修复 - 直接强制设置，不依赖任何其他方法
            self.ultimate_layout_fix()

            # 重新绘制画布
            self.canvas.draw_idle()

            # 重构：立即强制应用正确的布局，不等待延迟
            print("🔥 立即强制应用最终布局...")
            self.fig.subplots_adjust(left=0.15, bottom=0.60, right=0.95, top=0.80)
            self.canvas.draw_idle()

            # 验证最终布局
            pos = self.ax.get_position()
            print(f"🔥 最终验证底部边距: {pos.y0}")

            # 额外的延迟保护机制
            self.root.after(100, self.final_force_layout_fix)  # 使用draw_idle()提高性能

            # 延迟修复图表显示
            self.root.after(100, self.fix_chart_display)

        except Exception as e:
            self.ax.clear()
            self.ax.text(0.5, 0.5, f'预览错误: {str(e)}',
                        horizontalalignment='center', verticalalignment='center',
                        transform=self.ax.transAxes, fontsize=12, color='red')
            self.canvas.draw()

    def generate_chart(self):
        """生成完整图表"""
        if self.data is None:
            messagebox.showwarning("警告", "请先选择数据文件")
            return

        selected_columns = [col for col, var in self.column_vars.items() if var.get()]
        if not selected_columns:
            messagebox.showwarning("警告", "请选择要绘制的数据列")
            return

        try:
            # 获取图表尺寸
            width = float(self.width_var.get())
            height = float(self.height_var.get())

            # 创建新图表
            fig, ax = plt.subplots(figsize=(width, height))

            # 使用与预览相同的绘制逻辑
            self._plot_chart(ax, selected_columns)

            plt.show()
            messagebox.showinfo("成功", "图表生成完成！")

        except Exception as e:
            messagebox.showerror("错误", f"图表生成失败: {str(e)}")

    def _plot_chart(self, ax, selected_columns):
        """绘制图表的核心逻辑"""
        plot_data = self.data.copy()
        time_col = self.time_col_var.get()

        # 处理时间列
        if time_col and time_col in plot_data.columns:
            try:
                plot_data[time_col] = pd.to_datetime(plot_data[time_col])
                x_data = plot_data[time_col]
            except:
                x_data = range(len(plot_data))
        else:
            x_data = range(len(plot_data))

        # 获取颜色主题
        theme = self.themes.get(self.theme_var.get(), self.themes['默认'])
        theme_colors = theme['colors']

        # 绘制曲线
        chart_type = self.chart_type_var.get()

        for i, col in enumerate(selected_columns):
            if col in plot_data.columns:
                color = self.color_vars.get(col, theme_colors[i % len(theme_colors)])
                label = self.label_vars.get(col, tk.StringVar(value=col)).get()

                y_data = pd.to_numeric(plot_data[col], errors='coerce').dropna()
                x_data_clean = x_data[:len(y_data)]

                # 根据图表类型绘制
                if chart_type == '线图':
                    ax.plot(x_data_clean, y_data,
                           color=color,
                           linewidth=2,
                           label=label,
                           alpha=0.8)

                elif chart_type == '散点图':
                    ax.scatter(x_data_clean, y_data,
                              color=color,
                              label=label,
                              alpha=0.7,
                              s=30)

                elif chart_type == '柱状图':
                    ax.bar(x_data_clean, y_data,
                          color=color,
                          label=label,
                          alpha=0.7)

                elif chart_type == '面积图':
                    ax.fill_between(x_data_clean, y_data,
                                   color=color,
                                   label=label,
                                   alpha=0.5)

        # 设置图表样式
        ax.set_title(self.title_var.get(), fontsize=14, fontweight='bold', pad=20)
        ax.set_ylabel(self.ylabel_var.get(), fontsize=12)
        ax.set_xlabel('时间' if time_col else '数据点', fontsize=12)

        # 添加网格和图例
        ax.grid(True, alpha=0.3)
        ax.legend(loc='best', frameon=True, fancybox=True, shadow=True)

        # 格式化时间轴
        if time_col and pd.api.types.is_datetime64_any_dtype(plot_data[time_col]):
            ax.xaxis.set_major_formatter(mdates.DateFormatter('%H:%M'))
            plt.setp(ax.xaxis.get_majorticklabels(), rotation=45)

        # 美化图表
        ax.spines['top'].set_visible(False)
        ax.spines['right'].set_visible(False)

        # 注释掉tight_layout，因为它会覆盖我们的手动布局设置
        # plt.tight_layout()  # 这会覆盖subplots_adjust的设置，导致底部截断

    def save_chart(self):
        """保存图表"""
        if self.data is None:
            messagebox.showwarning("警告", "请先选择数据文件")
            return

        selected_columns = [col for col, var in self.column_vars.items() if var.get()]
        if not selected_columns:
            messagebox.showwarning("警告", "请选择要绘制的数据列")
            return

        # 选择保存路径
        file_path = filedialog.asksaveasfilename(
            title="保存图表",
            defaultextension=".png",
            filetypes=[
                ("PNG图片", "*.png"),
                ("JPG图片", "*.jpg"),
                ("PDF文件", "*.pdf"),
                ("SVG文件", "*.svg")
            ]
        )

        if not file_path:
            return

        try:
            # 获取图表尺寸
            width = float(self.width_var.get())
            height = float(self.height_var.get())

            # 创建图表
            fig, ax = plt.subplots(figsize=(width, height))
            self._plot_chart(ax, selected_columns)

            # 保存图表
            fig.savefig(file_path, dpi=300, bbox_inches='tight',
                       facecolor='white', edgecolor='none')
            plt.close(fig)

            messagebox.showinfo("成功", f"图表已保存到: {file_path}")

        except Exception as e:
            messagebox.showerror("错误", f"保存失败: {str(e)}")

    def show_fullscreen(self):
        """显示全屏预览"""
        if self.data is None:
            messagebox.showwarning("警告", "请先选择数据文件")
            return

        selected_columns = [col for col, var in self.column_vars.items() if var.get()]
        if not selected_columns:
            messagebox.showwarning("警告", "请选择要绘制的数据列")
            return

        try:
            # 创建全屏窗口
            fullscreen_window = tk.Toplevel(self.root)
            fullscreen_window.title("全屏预览")
            fullscreen_window.state('zoomed')  # Windows最大化

            # 创建图表
            fig, ax = plt.subplots(figsize=(16, 10))
            self._plot_chart(ax, selected_columns)

            # 嵌入到窗口
            canvas = FigureCanvasTkAgg(fig, fullscreen_window)
            canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)

            # 添加工具栏
            toolbar = NavigationToolbar2Tk(canvas, fullscreen_window)
            toolbar.update()

            # 添加关闭按钮
            button_frame = ttk.Frame(fullscreen_window)
            button_frame.pack(pady=10)

            ttk.Button(button_frame, text="关闭",
                      command=fullscreen_window.destroy).pack(side=tk.LEFT, padx=5)
            ttk.Button(button_frame, text="保存此图",
                      command=lambda: self.save_fullscreen_chart(fig)).pack(side=tk.LEFT, padx=5)

        except Exception as e:
            messagebox.showerror("错误", f"全屏预览失败: {str(e)}")

    def save_fullscreen_chart(self, fig):
        """保存全屏图表"""
        file_path = filedialog.asksaveasfilename(
            title="保存全屏图表",
            defaultextension=".png",
            filetypes=[
                ("PNG图片", "*.png"),
                ("JPG图片", "*.jpg"),
                ("PDF文件", "*.pdf")
            ]
        )

        if file_path:
            try:
                fig.savefig(file_path, dpi=300, bbox_inches='tight')
                messagebox.showinfo("成功", f"全屏图表已保存到: {file_path}")
            except Exception as e:
                messagebox.showerror("错误", f"保存失败: {str(e)}")

    def reset_all(self):
        """重置所有设置"""
        # 重置文件相关
        self.data = None
        self.file_path = None
        self.file_path_var.set("")
        self.sheet_var.set("")
        self.sheet_combo['values'] = []

        # 重置列选择
        self.time_col_var.set("")
        self.time_combo['values'] = []

        for widget in self.columns_frame.winfo_children():
            widget.destroy()
        self.column_vars.clear()
        self.label_vars.clear()
        self.color_vars.clear()

        # 重置图表配置
        self.title_var.set("数据对比图")
        self.ylabel_var.set("数值")
        self.chart_type_var.set("线图")
        self.theme_var.set("默认")
        self.width_var.set("12")
        self.height_var.set("8")

        # 重置预览
        self.ax.clear()
        self.ax.text(0.5, 0.5, '请选择数据文件开始',
                    horizontalalignment='center', verticalalignment='center',
                    transform=self.ax.transAxes, fontsize=16)
        self.ax.set_xticks([])
        self.ax.set_yticks([])
        self.canvas.draw()

        messagebox.showinfo("完成", "所有设置已重置")

    def show_excel_error_dialog(self, error_message):
        """显示Excel错误对话框，提供详细的解决方案"""
        error_window = tk.Toplevel(self.root)
        error_window.title("Excel文件加载失败")
        error_window.geometry("700x600")
        error_window.transient(self.root)
        error_window.grab_set()

        # 主框架
        main_frame = ttk.Frame(error_window)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # 错误图标和标题
        title_frame = ttk.Frame(main_frame)
        title_frame.pack(fill=tk.X, pady=(0, 20))

        ttk.Label(title_frame, text="⚠️ Excel文件兼容性问题",
                 font=("Arial", 14, "bold"), foreground="red").pack(anchor=tk.W)

        # 错误信息
        error_frame = ttk.LabelFrame(main_frame, text="错误详情", padding=10)
        error_frame.pack(fill=tk.X, pady=(0, 20))

        error_text = tk.Text(error_frame, height=6, wrap=tk.WORD)
        error_scroll = ttk.Scrollbar(error_frame, orient="vertical", command=error_text.yview)
        error_text.configure(yscrollcommand=error_scroll.set)

        error_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        error_scroll.pack(side=tk.RIGHT, fill=tk.Y)

        error_text.insert(tk.END, error_message)
        error_text.config(state=tk.DISABLED)

        # 解决方案
        solution_frame = ttk.LabelFrame(main_frame, text="解决方案", padding=10)
        solution_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 20))

        solutions_text = """🔧 立即解决方案：

方案1：重新保存Excel文件
1. 用Microsoft Excel打开问题文件
2. 选择"文件" → "另存为"
3. 选择"Excel工作簿(*.xlsx)"格式
4. 保存为新文件名，然后重新加载

方案2：导出为CSV格式
1. 在Excel中打开文件
2. 选择"文件" → "另存为"
3. 选择"CSV UTF-8(逗号分隔)(*.csv)"格式
4. 保存后用本程序加载CSV文件

方案3：清除格式
1. 在Excel中选择所有数据（Ctrl+A）
2. 复制数据（Ctrl+C）
3. 右键选择"选择性粘贴"
4. 选择"数值"选项，确定
5. 另存为新的Excel文件

方案4：使用在线转换工具
1. 搜索"Excel在线转换"
2. 上传文件并转换为标准格式
3. 下载转换后的文件

🔍 问题原因：
您的Excel文件包含了与当前openpyxl库版本不兼容的样式信息。这通常发生在：
- 文件包含复杂的格式和样式
- 文件由较新或较旧版本的Excel创建
- 文件包含宏或特殊功能

💡 预防措施：
- 使用标准的Excel格式
- 避免过度复杂的样式
- 定期更新Python包"""

        solution_text = tk.Text(solution_frame, wrap=tk.WORD)
        solution_scroll = ttk.Scrollbar(solution_frame, orient="vertical", command=solution_text.yview)
        solution_text.configure(yscrollcommand=solution_scroll.set)

        solution_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        solution_scroll.pack(side=tk.RIGHT, fill=tk.Y)

        solution_text.insert(tk.END, solutions_text)
        solution_text.config(state=tk.DISABLED)

        # 按钮
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X)

        ttk.Button(button_frame, text="复制错误信息",
                  command=lambda: self.copy_to_clipboard(error_message)).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="尝试转换为CSV",
                  command=lambda: self.convert_to_csv_and_reload(error_window)).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="打开文件位置",
                  command=self.open_file_location).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="关闭",
                  command=error_window.destroy).pack(side=tk.RIGHT)

    def copy_to_clipboard(self, text):
        """复制文本到剪贴板"""
        try:
            self.root.clipboard_clear()
            self.root.clipboard_append(text)
            messagebox.showinfo("成功", "错误信息已复制到剪贴板")
        except Exception as e:
            messagebox.showerror("错误", f"复制失败: {e}")

    def open_file_location(self):
        """打开文件所在位置"""
        if self.file_path and os.path.exists(self.file_path):
            try:
                import subprocess
                subprocess.run(['explorer', '/select,', self.file_path])
            except Exception as e:
                messagebox.showerror("错误", f"无法打开文件位置: {e}")
        else:
            messagebox.showwarning("警告", "文件路径无效")

    def convert_to_csv_and_reload(self, error_window):
        """尝试转换Excel文件为CSV并重新加载"""
        if not self.file_path or not os.path.exists(self.file_path):
            messagebox.showerror("错误", "没有有效的文件路径")
            return

        try:
            # 关闭错误窗口
            error_window.destroy()

            # 显示转换进度
            progress_window = self.show_conversion_progress()

            # 尝试多种方法转换为CSV
            csv_file = self.excel_to_csv_converter(self.file_path, progress_window)

            if csv_file:
                # 转换成功，更新文件路径并重新加载
                self.file_path = csv_file
                self.file_path_var.set(os.path.basename(csv_file))

                # 关闭进度窗口
                progress_window.destroy()

                # 重新加载CSV文件
                self.load_csv_file()

                messagebox.showinfo("转换成功",
                                  f"Excel文件已成功转换为CSV格式！\n"
                                  f"新文件：{os.path.basename(csv_file)}\n\n"
                                  f"数据已自动加载，您现在可以正常使用所有功能。")
            else:
                progress_window.destroy()
                messagebox.showerror("转换失败",
                                   "自动转换失败。请尝试手动转换：\n"
                                   "1. 用Excel打开文件\n"
                                   "2. 另存为CSV格式\n"
                                   "3. 在程序中加载CSV文件")

        except Exception as e:
            if 'progress_window' in locals():
                progress_window.destroy()
            messagebox.showerror("转换错误", f"转换过程中出现错误：{str(e)}")

    def show_conversion_progress(self):
        """显示转换进度窗口"""
        progress_window = tk.Toplevel(self.root)
        progress_window.title("正在转换文件")
        progress_window.geometry("400x200")
        progress_window.transient(self.root)
        progress_window.grab_set()

        # 居中显示
        progress_window.update_idletasks()
        x = (progress_window.winfo_screenwidth() // 2) - (400 // 2)
        y = (progress_window.winfo_screenheight() // 2) - (200 // 2)
        progress_window.geometry(f"400x200+{x}+{y}")

        # 内容
        main_frame = ttk.Frame(progress_window)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        ttk.Label(main_frame, text="🔄 正在转换Excel文件为CSV格式",
                 font=("Arial", 12, "bold")).pack(pady=(0, 10))

        ttk.Label(main_frame, text="请稍候，程序正在尝试多种转换方法...").pack(pady=(0, 20))

        # 进度条
        progress_bar = ttk.Progressbar(main_frame, mode='indeterminate')
        progress_bar.pack(fill=tk.X, pady=(0, 20))
        progress_bar.start()

        # 状态标签
        status_label = ttk.Label(main_frame, text="正在分析文件...")
        status_label.pack()

        # 存储状态标签引用
        progress_window.status_label = status_label
        progress_window.progress_bar = progress_bar

        progress_window.update()
        return progress_window

    def excel_to_csv_converter(self, excel_file, progress_window):
        """Excel转CSV转换器，尝试多种方法"""
        base_name = os.path.splitext(excel_file)[0]
        csv_file = f"{base_name}_converted.csv"

        # 转换方法列表
        conversion_methods = [
            ("使用openpyxl直接读取", self._convert_method_openpyxl_direct),
            ("使用pandas标准方法", self._convert_method_pandas_standard),
            ("使用xlrd引擎", self._convert_method_xlrd),
            ("强制二进制读取", self._convert_method_binary_xml),
            ("使用LibreOffice转换", self._convert_method_libreoffice),
        ]

        for i, (method_name, method_func) in enumerate(conversion_methods):
            try:
                # 更新进度
                progress_window.status_label.config(text=f"尝试方法 {i+1}/{len(conversion_methods)}: {method_name}")
                progress_window.update()

                # 尝试转换
                if method_func(excel_file, csv_file):
                    progress_window.status_label.config(text=f"✓ 转换成功：{method_name}")
                    progress_window.update()
                    return csv_file

            except Exception as e:
                print(f"转换方法 {method_name} 失败: {e}")
                continue

        return None

    def _convert_method_openpyxl_direct(self, excel_file, csv_file):
        """方法1：直接使用openpyxl读取数据"""
        import openpyxl

        # 尝试不同的加载参数
        load_options = [
            {'data_only': True, 'read_only': True, 'keep_vba': False},
            {'data_only': True, 'read_only': False, 'keep_vba': False},
            {'read_only': True, 'keep_vba': False},
            {'data_only': True},
            {}
        ]

        for options in load_options:
            try:
                wb = openpyxl.load_workbook(excel_file, **options)
                ws = wb.active

                # 提取数据
                data = []
                for row in ws.iter_rows(values_only=True):
                    if any(cell is not None for cell in row):
                        data.append(row)

                if data:
                    # 写入CSV
                    import csv
                    with open(csv_file, 'w', encoding='utf-8-sig', newline='') as f:
                        writer = csv.writer(f)
                        for row in data:
                            clean_row = [str(cell) if cell is not None else '' for cell in row]
                            writer.writerow(clean_row)

                    return True

            except Exception:
                continue

        return False

    def _convert_method_pandas_standard(self, excel_file, csv_file):
        """方法2：使用pandas标准方法"""
        try:
            df = pd.read_excel(excel_file)
            df.to_csv(csv_file, index=False, encoding='utf-8-sig')
            return True
        except Exception:
            return False

    def _convert_method_xlrd(self, excel_file, csv_file):
        """方法3：使用xlrd引擎（适用于.xls文件）"""
        if not excel_file.lower().endswith('.xls'):
            return False

        try:
            df = pd.read_excel(excel_file, engine='xlrd')
            df.to_csv(csv_file, index=False, encoding='utf-8-sig')
            return True
        except Exception:
            return False

    def _convert_method_binary_xml(self, excel_file, csv_file):
        """方法4：强制二进制XML读取（保留表头的改进版本）"""
        try:
            import zipfile
            import xml.etree.ElementTree as ET

            # Excel文件实际上是ZIP文件
            with zipfile.ZipFile(excel_file, 'r') as zip_file:
                # 读取共享字符串（包含文本内容）
                shared_strings = {}
                try:
                    if 'xl/sharedStrings.xml' in zip_file.namelist():
                        shared_strings_xml = zip_file.read('xl/sharedStrings.xml')
                        shared_root = ET.fromstring(shared_strings_xml)

                        for i, si_elem in enumerate(shared_root.findall('.//{http://schemas.openxmlformats.org/spreadsheetml/2006/main}si')):
                            t_elem = si_elem.find('.//{http://schemas.openxmlformats.org/spreadsheetml/2006/main}t')
                            if t_elem is not None:
                                shared_strings[str(i)] = t_elem.text
                except Exception as e:
                    print(f"读取共享字符串失败: {e}")

                # 查找工作表XML文件
                sheet_files = [f for f in zip_file.namelist()
                              if f.startswith('xl/worksheets/')]

                if not sheet_files:
                    return False

                # 读取第一个工作表
                sheet_file = sheet_files[0]
                xml_content = zip_file.read(sheet_file)

                # 解析XML
                root = ET.fromstring(xml_content)

                # 提取数据，按行列位置组织
                cell_data = {}

                # 遍历所有单元格
                for c_elem in root.findall('.//{http://schemas.openxmlformats.org/spreadsheetml/2006/main}c'):
                    # 获取单元格位置
                    r_attr = c_elem.get('r')  # 如 "A1", "B2"
                    if not r_attr:
                        continue

                    # 解析行列位置
                    col_str = ''.join([c for c in r_attr if c.isalpha()])
                    row_str = ''.join([c for c in r_attr if c.isdigit()])

                    if not row_str:
                        continue

                    row_num = int(row_str) - 1  # 转为0基索引
                    col_num = self._excel_col_to_num(col_str)

                    # 获取单元格值
                    cell_value = ""
                    t_attr = c_elem.get('t')  # 单元格类型

                    if t_attr == 's':  # 共享字符串
                        v_elem = c_elem.find('.//{http://schemas.openxmlformats.org/spreadsheetml/2006/main}v')
                        if v_elem is not None:
                            string_index = v_elem.text
                            cell_value = shared_strings.get(string_index, v_elem.text)
                    elif t_attr == 'inlineStr':  # 内联字符串（关键！这是表头文本）
                        # 查找内联字符串文本
                        is_elem = c_elem.find('.//{http://schemas.openxmlformats.org/spreadsheetml/2006/main}is')
                        if is_elem is not None:
                            t_elem = is_elem.find('.//{http://schemas.openxmlformats.org/spreadsheetml/2006/main}t')
                            if t_elem is not None:
                                cell_value = t_elem.text
                    else:  # 数值或其他
                        v_elem = c_elem.find('.//{http://schemas.openxmlformats.org/spreadsheetml/2006/main}v')
                        if v_elem is not None:
                            cell_value = v_elem.text

                    # 存储单元格数据
                    if row_num not in cell_data:
                        cell_data[row_num] = {}
                    cell_data[row_num][col_num] = cell_value

                # 转换为行列表
                if not cell_data:
                    return False

                max_row = max(cell_data.keys())
                max_col = max(max(row_data.keys()) for row_data in cell_data.values()) if cell_data else 0

                rows = []
                for row_num in range(max_row + 1):
                    row = []
                    for col_num in range(max_col + 1):
                        cell_value = cell_data.get(row_num, {}).get(col_num, '')
                        row.append(cell_value)

                    # 只添加非空行
                    if any(str(cell).strip() and str(cell).strip().lower() != 'none' for cell in row):
                        rows.append(row)

                if rows:
                    # 检查第一行是否为空或无效表头
                    first_row_empty = True
                    if rows:
                        first_row = rows[0]
                        for cell in first_row:
                            if str(cell).strip() and str(cell).strip().lower() not in ['none', '', 'null']:
                                first_row_empty = False
                                break

                    # 如果第一行是空的，生成有意义的列名
                    if first_row_empty and len(rows) > 1:
                        print("  检测到空表头，生成列名...")
                        # 生成列名
                        num_cols = len(rows[1]) if len(rows) > 1 else len(rows[0])
                        header_row = []

                        # 根据数据特征生成列名
                        for i in range(num_cols):
                            if i == 0:
                                header_row.append("时间")  # 第一列通常是时间
                            else:
                                header_row.append(f"数据{i}")

                        # 替换第一行或添加表头
                        if first_row_empty:
                            rows[0] = header_row
                        else:
                            rows.insert(0, header_row)

                    # 写入CSV
                    import csv
                    with open(csv_file, 'w', encoding='utf-8-sig', newline='') as f:
                        writer = csv.writer(f)
                        for row in rows:
                            clean_row = [str(cell) if cell is not None else '' for cell in row]
                            writer.writerow(clean_row)

                    return True
                else:
                    return False

        except Exception as e:
            print(f"二进制XML读取失败: {e}")
            import traceback
            traceback.print_exc()
            return False

    def _excel_col_to_num(self, col_str):
        """将Excel列名（如A, B, AA）转换为数字索引"""
        num = 0
        for c in col_str:
            num = num * 26 + (ord(c.upper()) - ord('A')) + 1
        return num - 1  # 转为0基索引

    def _convert_method_libreoffice(self, excel_file, csv_file):
        """方法4：使用LibreOffice转换"""
        import subprocess
        import tempfile

        # LibreOffice可能的路径
        libreoffice_paths = [
            r"C:\Program Files\LibreOffice\program\soffice.exe",
            r"C:\Program Files (x86)\LibreOffice\program\soffice.exe",
            "soffice",
            "libreoffice"
        ]

        soffice_path = None
        for path in libreoffice_paths:
            try:
                result = subprocess.run([path, "--version"],
                                      capture_output=True, timeout=5)
                if result.returncode == 0:
                    soffice_path = path
                    break
            except:
                continue

        if not soffice_path:
            return False

        try:
            # 创建临时目录
            temp_dir = tempfile.mkdtemp()

            # 转换命令
            cmd = [
                soffice_path,
                "--headless",
                "--convert-to", "csv",
                "--outdir", temp_dir,
                excel_file
            ]

            result = subprocess.run(cmd, capture_output=True, timeout=30)

            if result.returncode == 0:
                # 查找生成的CSV文件
                base_name = os.path.splitext(os.path.basename(excel_file))[0]
                temp_csv = os.path.join(temp_dir, f"{base_name}.csv")

                if os.path.exists(temp_csv):
                    # 移动到目标位置
                    import shutil
                    shutil.move(temp_csv, csv_file)
                    shutil.rmtree(temp_dir)
                    return True

            # 清理临时目录
            import shutil
            shutil.rmtree(temp_dir)
            return False

        except Exception:
            return False

    def load_csv_file(self):
        """加载CSV文件"""
        try:
            self.status_var.set("正在加载CSV文件...")
            self.root.update()

            # 尝试不同编码
            encodings = ['utf-8-sig', 'utf-8', 'gbk', 'gb2312']

            for encoding in encodings:
                try:
                    self.data = pd.read_csv(self.file_path, encoding=encoding)
                    break
                except UnicodeDecodeError:
                    continue
            else:
                raise ValueError("无法识别CSV文件编码")

            # 设置工作表信息
            self.sheet_names = ["CSV数据"]
            self.sheet_combo['values'] = self.sheet_names
            self.sheet_var.set(self.sheet_names[0])

            # 处理数据
            # 检查是否需要处理数值型表头
            self.check_and_fix_numeric_headers()

            self.process_data()

            self.status_var.set(f"CSV文件加载成功！数据形状: {self.data.shape}")

        except Exception as e:
            self.status_var.set("CSV文件加载失败")
            messagebox.showerror("错误", f"CSV文件加载失败: {str(e)}")

    def convert_current_file_to_csv(self):
        """主动转换当前文件为CSV格式"""
        if not self.file_path:
            # 如果没有当前文件，让用户选择文件
            file_path = filedialog.askopenfilename(
                title="选择要转换的Excel文件",
                filetypes=[
                    ("Excel文件", "*.xlsx;*.xls"),
                    ("所有文件", "*.*")
                ]
            )
            if not file_path:
                return
            self.file_path = file_path
            self.file_path_var.set(os.path.basename(file_path))

        # 检查文件类型
        file_ext = os.path.splitext(self.file_path)[1].lower()
        if file_ext == '.csv':
            messagebox.showinfo("提示", "当前文件已经是CSV格式，无需转换。")
            return

        if file_ext not in ['.xlsx', '.xls']:
            messagebox.showwarning("警告", "只能转换Excel文件（.xlsx或.xls格式）。")
            return

        # 确认转换
        result = messagebox.askyesno("确认转换",
                                   f"是否将文件转换为CSV格式？\n\n"
                                   f"原文件：{os.path.basename(self.file_path)}\n"
                                   f"转换后：{os.path.splitext(os.path.basename(self.file_path))[0]}.csv\n\n"
                                   f"转换后将自动加载CSV文件。")

        if not result:
            return

        try:
            # 显示转换进度
            progress_window = self.show_conversion_progress()

            # 执行转换
            csv_file = self.excel_to_csv_converter(self.file_path, progress_window)

            if csv_file:
                # 转换成功
                progress_window.destroy()

                # 更新文件路径
                self.file_path = csv_file
                self.file_path_var.set(os.path.basename(csv_file))

                # 重新加载文件
                self.load_csv_file()

                messagebox.showinfo("转换成功",
                                  f"Excel文件已成功转换为CSV格式！\n\n"
                                  f"新文件：{os.path.basename(csv_file)}\n"
                                  f"数据形状：{self.data.shape if self.data is not None else '未知'}\n\n"
                                  f"您现在可以正常使用所有功能。")
            else:
                progress_window.destroy()

                # 转换失败，提供手动指导
                self.show_manual_conversion_guide()

        except Exception as e:
            if 'progress_window' in locals():
                progress_window.destroy()
            messagebox.showerror("转换错误", f"转换过程中出现错误：{str(e)}")

    def show_manual_conversion_guide(self):
        """显示手动转换指导"""
        guide_window = tk.Toplevel(self.root)
        guide_window.title("手动转换指导")
        guide_window.geometry("600x500")
        guide_window.transient(self.root)
        guide_window.grab_set()

        # 居中显示
        guide_window.update_idletasks()
        x = (guide_window.winfo_screenwidth() // 2) - (600 // 2)
        y = (guide_window.winfo_screenheight() // 2) - (500 // 2)
        guide_window.geometry(f"600x500+{x}+{y}")

        # 主框架
        main_frame = ttk.Frame(guide_window)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # 标题
        ttk.Label(main_frame, text="📋 手动转换指导",
                 font=("Arial", 14, "bold")).pack(anchor=tk.W, pady=(0, 20))

        # 指导内容
        guide_text = f"""自动转换失败，请按照以下步骤手动转换：

🔧 方法1：Excel另存为CSV（推荐）
1. 用Microsoft Excel打开文件：{os.path.basename(self.file_path)}
2. 点击"文件"菜单
3. 选择"另存为"
4. 在"保存类型"中选择"CSV UTF-8(逗号分隔)(*.csv)"
5. 点击"保存"
6. 在本程序中点击"浏览"加载转换后的CSV文件

🔧 方法2：清除格式后保存
1. 在Excel中打开文件
2. 选择所有数据（Ctrl+A）
3. 复制数据（Ctrl+C）
4. 新建工作簿
5. 选择性粘贴 → 数值
6. 另存为Excel格式或CSV格式

🔧 方法3：使用在线转换工具
1. 搜索"Excel转CSV在线工具"
2. 上传您的Excel文件
3. 下载转换后的CSV文件
4. 在本程序中加载CSV文件

💡 为什么需要转换？
您的Excel文件包含了与当前openpyxl库不兼容的样式信息。
转换为CSV格式可以去除所有样式，只保留数据内容，
确保100%的兼容性。

✅ 转换完成后的优势：
• 文件加载速度更快
• 完全兼容所有功能
• 文件体积更小
• 跨平台兼容性更好"""

        # 文本框
        text_frame = ttk.Frame(main_frame)
        text_frame.pack(fill=tk.BOTH, expand=True)

        text_widget = tk.Text(text_frame, wrap=tk.WORD, font=("Consolas", 9))
        scrollbar = ttk.Scrollbar(text_frame, orient="vertical", command=text_widget.yview)
        text_widget.configure(yscrollcommand=scrollbar.set)

        text_widget.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        text_widget.insert(tk.END, guide_text)
        text_widget.config(state=tk.DISABLED)

        # 按钮
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=(20, 0))

        ttk.Button(button_frame, text="打开文件位置",
                  command=self.open_file_location).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="重新尝试转换",
                  command=lambda: [guide_window.destroy(), self.convert_current_file_to_csv()]).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="关闭",
                  command=guide_window.destroy).pack(side=tk.RIGHT)

    def check_and_fix_numeric_headers(self):
        """检查并修复数值型表头"""
        if self.data is None or self.data.empty:
            return

        # 检查列名是否都是数值型
        numeric_headers = []
        for col in self.data.columns:
            try:
                float(str(col))
                numeric_headers.append(col)
            except ValueError:
                pass

        # 如果大部分列名都是数值型，询问用户是否要重命名
        if len(numeric_headers) >= len(self.data.columns) * 0.8:  # 80%以上是数值
            self.show_header_options_dialog(numeric_headers)

    def show_header_options_dialog(self, numeric_headers):
        """显示表头选项对话框"""
        dialog = tk.Toplevel(self.root)
        dialog.title("数值型表头处理")
        dialog.geometry("600x400")
        dialog.transient(self.root)
        dialog.grab_set()

        # 居中显示
        dialog.update_idletasks()
        x = (dialog.winfo_screenwidth() // 2) - (600 // 2)
        y = (dialog.winfo_screenheight() // 2) - (400 // 2)
        dialog.geometry(f"600x400+{x}+{y}")

        # 主框架
        main_frame = ttk.Frame(dialog)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # 标题
        ttk.Label(main_frame, text="🔧 检测到数值型表头",
                 font=("Arial", 14, "bold")).pack(anchor=tk.W, pady=(0, 10))

        # 说明
        info_text = f"""您的数据文件包含数值型的列名：
{', '.join([str(h)[:15] + '...' if len(str(h)) > 15 else str(h) for h in numeric_headers[:5]])}
{'...' if len(numeric_headers) > 5 else ''}

这可能是：
• 时间戳或测量时间点
• 传感器编号或测量点ID
• 其他数值型标识符

请选择如何处理这些列名："""

        ttk.Label(main_frame, text=info_text, justify=tk.LEFT).pack(anchor=tk.W, pady=(0, 20))

        # 选项
        self.header_choice = tk.StringVar(value="keep")

        ttk.Radiobutton(main_frame, text="保留原始数值列名",
                       variable=self.header_choice, value="keep").pack(anchor=tk.W, pady=2)
        ttk.Radiobutton(main_frame, text="生成描述性列名（列1, 列2, 列3...）",
                       variable=self.header_choice, value="descriptive").pack(anchor=tk.W, pady=2)
        ttk.Radiobutton(main_frame, text="生成数据类型列名（数据1, 数据2, 数据3...）",
                       variable=self.header_choice, value="data").pack(anchor=tk.W, pady=2)
        ttk.Radiobutton(main_frame, text="生成传感器列名（传感器1, 传感器2...）",
                       variable=self.header_choice, value="sensor").pack(anchor=tk.W, pady=2)

        # 预览框架
        preview_frame = ttk.LabelFrame(main_frame, text="列名预览", padding=10)
        preview_frame.pack(fill=tk.BOTH, expand=True, pady=(20, 0))

        self.preview_text = tk.Text(preview_frame, height=6, wrap=tk.WORD)
        preview_scroll = ttk.Scrollbar(preview_frame, orient="vertical", command=self.preview_text.yview)
        self.preview_text.configure(yscrollcommand=preview_scroll.set)

        self.preview_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        preview_scroll.pack(side=tk.RIGHT, fill=tk.Y)

        # 绑定选项变化事件
        for widget in main_frame.winfo_children():
            if isinstance(widget, ttk.Radiobutton):
                widget.configure(command=lambda: self.update_header_preview(numeric_headers))

        # 初始预览
        self.update_header_preview(numeric_headers)

        # 按钮
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=(20, 0))

        ttk.Button(button_frame, text="应用",
                  command=lambda: self.apply_header_choice(dialog, numeric_headers)).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="取消",
                  command=dialog.destroy).pack(side=tk.LEFT)

    def update_header_preview(self, numeric_headers):
        """更新表头预览"""
        choice = self.header_choice.get()

        if choice == "keep":
            preview = "保留原始列名:\n" + "\n".join([f"列 {i+1}: {h}" for i, h in enumerate(numeric_headers[:10])])
        elif choice == "descriptive":
            preview = "描述性列名:\n" + "\n".join([f"列 {i+1}: 列{i+1}" for i in range(min(10, len(numeric_headers)))])
        elif choice == "data":
            preview = "数据类型列名:\n" + "\n".join([f"列 {i+1}: 数据{i+1}" for i in range(min(10, len(numeric_headers)))])
        elif choice == "sensor":
            preview = "传感器列名:\n" + "\n".join([f"列 {i+1}: 传感器{i+1}" for i in range(min(10, len(numeric_headers)))])

        if len(numeric_headers) > 10:
            preview += f"\n... 还有 {len(numeric_headers) - 10} 列"

        self.preview_text.delete(1.0, tk.END)
        self.preview_text.insert(tk.END, preview)

    def apply_header_choice(self, dialog, numeric_headers):
        """应用表头选择"""
        choice = self.header_choice.get()

        if choice != "keep":
            # 创建新的列名映射
            new_columns = {}

            for i, old_col in enumerate(self.data.columns):
                if choice == "descriptive":
                    new_columns[old_col] = f"列{i+1}"
                elif choice == "data":
                    new_columns[old_col] = f"数据{i+1}"
                elif choice == "sensor":
                    new_columns[old_col] = f"传感器{i+1}"

            # 重命名列
            self.data = self.data.rename(columns=new_columns)

            # 更新状态
            self.status_var.set(f"列名已更新！数据形状: {self.data.shape}")

            messagebox.showinfo("完成", f"列名已更新为{choice}格式！\n新列名: {list(self.data.columns)[:5]}{'...' if len(self.data.columns) > 5 else ''}")

        dialog.destroy()

    def create_tooltip(self, widget, text):
        """为控件创建工具提示"""
        def on_enter(event):
            tooltip = tk.Toplevel()
            tooltip.wm_overrideredirect(True)
            tooltip.wm_geometry(f"+{event.x_root+10}+{event.y_root+10}")

            label = tk.Label(tooltip, text=text, background="lightyellow",
                           relief="solid", borderwidth=1, font=("Arial", 9))
            label.pack()

            widget.tooltip = tooltip

        def on_leave(event):
            if hasattr(widget, 'tooltip'):
                widget.tooltip.destroy()
                del widget.tooltip

        widget.bind("<Enter>", on_enter)
        widget.bind("<Leave>", on_leave)

    def show_compatibility_help(self):
        """显示Excel兼容性帮助"""
        help_window = tk.Toplevel(self.root)
        help_window.title("Excel兼容性帮助")
        help_window.geometry("600x500")
        help_window.transient(self.root)

        # 主框架
        main_frame = ttk.Frame(help_window)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # 标题
        ttk.Label(main_frame, text="📚 Excel兼容性问题解决指南",
                 font=("Arial", 14, "bold")).pack(anchor=tk.W, pady=(0, 20))

        # 帮助内容
        help_text = """🔧 常见Excel兼容性问题及解决方案：

1. 样式兼容性错误
   问题：TypeError: expected <class 'openpyxl.styles.fills.Fill'>
   解决：文件包含不兼容的样式信息

2. 立即解决方案：
   ✅ 用Excel重新保存文件（推荐）
      - 打开Excel文件
      - 另存为 → Excel工作簿(.xlsx)
      - 使用新文件名保存

   ✅ 导出为CSV格式
      - 另存为 → CSV UTF-8格式
      - 本程序完全支持CSV文件

   ✅ 清除复杂格式
      - 选择所有数据（Ctrl+A）
      - 选择性粘贴 → 仅数值
      - 另存为新文件

3. 预防措施：
   • 避免过度复杂的Excel格式
   • 定期更新Python包
   • 使用标准的Excel功能
   • 备份重要数据文件

4. 程序特性：
   • 自动尝试6种不同的读取方法
   • 智能错误处理和恢复
   • 详细的错误信息和解决建议
   • 支持多种文件格式

5. 支持的文件格式：
   • Excel 2007+ (.xlsx) ✅
   • Excel 97-2003 (.xls) ✅
   • CSV文件 (.csv) ✅
   • 文本文件 (.txt) ✅

💡 提示：如果问题持续存在，请尝试在不同的计算机上打开文件，
或联系技术支持获取进一步帮助。"""

        # 文本框
        text_frame = ttk.Frame(main_frame)
        text_frame.pack(fill=tk.BOTH, expand=True)

        text_widget = tk.Text(text_frame, wrap=tk.WORD, font=("Consolas", 9))
        scrollbar = ttk.Scrollbar(text_frame, orient="vertical", command=text_widget.yview)
        text_widget.configure(yscrollcommand=scrollbar.set)

        text_widget.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        text_widget.insert(tk.END, help_text)
        text_widget.config(state=tk.DISABLED)

        # 按钮
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=(20, 0))

        ttk.Button(button_frame, text="关闭",
                  command=help_window.destroy).pack(side=tk.RIGHT)

def main():
    """主函数"""
    # 创建主窗口
    root = tk.Tk()

    # 创建应用（依赖检查在__init__中进行）
    app = CompleteDataVisualizerGUI(root)

    # 设置窗口属性
    try:
        root.state('zoomed')  # Windows最大化
    except:
        pass

    # 启动GUI
    root.mainloop()

if __name__ == "__main__":
    main()
