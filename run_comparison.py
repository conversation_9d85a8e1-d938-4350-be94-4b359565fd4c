#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试11 vs 测试5 对比分析启动器
使用现有的GUI程序功能来生成对比图表
"""

import os
import sys
import tkinter as tk
from tkinter import messagebox

def check_files():
    """检查必要的文件是否存在"""
    required_files = ['11_fixed_headers.csv', '5_fixed_headers.csv', 'complete_gui_plotter.py']
    missing_files = []
    
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    return missing_files

def create_comparison_interface():
    """创建对比分析界面"""
    root = tk.Tk()
    root.title("测试11 vs 测试5 - 折线图对比分析")
    root.geometry("800x600")
    
    # 标题
    title_frame = tk.Frame(root)
    title_frame.pack(pady=20)
    
    title_label = tk.Label(title_frame, text="测试结果对比分析工具", 
                          font=("Arial", 18, "bold"), fg="blue")
    title_label.pack()
    
    subtitle_label = tk.Label(title_frame, text="测试11 vs 测试5 - 折线图对比", 
                             font=("Arial", 14), fg="gray")
    subtitle_label.pack(pady=5)
    
    # 状态显示
    status_var = tk.StringVar(value="准备就绪")
    status_label = tk.Label(root, textvariable=status_var, font=("Arial", 12))
    status_label.pack(pady=10)
    
    # 按钮框架
    button_frame = tk.Frame(root)
    button_frame.pack(pady=30)
    
    def run_comparison():
        """运行对比分析"""
        try:
            status_var.set("正在启动对比分析...")
            root.update()
            
            # 导入现有的GUI程序
            sys.path.insert(0, os.getcwd())
            
            # 尝试导入并运行现有程序
            try:
                import complete_gui_plotter
                
                # 检查依赖
                temp_root = tk.Tk()
                temp_root.withdraw()
                
                app = complete_gui_plotter.CompleteDataVisualizerGUI(temp_root)
                
                # 检查pandas和matplotlib是否可用
                if not app.dep_manager.dependencies['pandas']['available']:
                    temp_root.destroy()
                    messagebox.showerror("依赖缺失", "pandas库不可用。请安装pandas:\npip install pandas")
                    status_var.set("依赖缺失")
                    return
                
                if not app.dep_manager.dependencies['matplotlib']['available']:
                    temp_root.destroy()
                    messagebox.showerror("依赖缺失", "matplotlib库不可用。请安装matplotlib:\npip install matplotlib")
                    status_var.set("依赖缺失")
                    return
                
                # 获取模块
                pd = app.dep_manager.get_module('pandas')
                plt_modules = app.dep_manager.get_module('matplotlib')
                plt = plt_modules['plt']
                
                temp_root.destroy()
                
                # 读取数据
                status_var.set("正在读取数据...")
                root.update()
                
                df11 = pd.read_csv('11_fixed_headers.csv')
                df5 = pd.read_csv('5_fixed_headers.csv')
                
                # 设置中文字体
                plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
                plt.rcParams['axes.unicode_minus'] = False
                
                status_var.set("正在生成对比图表...")
                root.update()
                
                # 创建对比图表
                create_comparison_plots(df11, df5, plt)
                
                status_var.set("对比图表生成完成！")
                messagebox.showinfo("成功", "对比图表已生成并保存！\n\n生成的文件：\n• complete_comparison.png\n• key_parameters_comparison.png\n• cooling_effect_comparison.png")
                
            except ImportError as e:
                messagebox.showerror("导入错误", f"无法导入绘图程序: {e}")
                status_var.set("导入失败")
            
        except Exception as e:
            status_var.set(f"错误: {e}")
            messagebox.showerror("错误", f"对比分析失败: {e}")
    
    def create_comparison_plots(df11, df5, plt):
        """创建对比图表"""
        # 温度参数
        temp_columns = [
            '遮阳罩表面温度', '遮阳罩背面温度', '遮阳罩皮革表面温度',
            '制冷帐篷表面温度', '制冷帐篷背面温度', '制冷帐篷皮革温度',
            '皮革表面温度', '皮革背面温度', '环境温度'
        ]
        
        # 1. 完整对比图 (3x3)
        fig1, axes = plt.subplots(3, 3, figsize=(20, 16))
        fig1.suptitle('测试11 vs 测试5 - 完整温度对比折线图', fontsize=18, fontweight='bold')
        
        colors = {'11': '#FF6B6B', '5': '#4ECDC4'}
        
        for i, col in enumerate(temp_columns):
            row = i // 3
            col_idx = i % 3
            ax = axes[row, col_idx]
            
            # 绘制折线
            ax.plot(df11['时间']/60, df11[col], 
                   color=colors['11'], linewidth=2, alpha=0.8, label='测试11')
            ax.plot(df5['时间']/60, df5[col], 
                   color=colors['5'], linewidth=2, alpha=0.8, label='测试5')
            
            ax.set_title(col, fontsize=12, fontweight='bold')
            ax.set_xlabel('时间 (分钟)', fontsize=10)
            ax.set_ylabel('温度 (°C)', fontsize=10)
            ax.legend(fontsize=9)
            ax.grid(True, alpha=0.3)
            
            # 添加平均值差异
            avg_11 = df11[col].mean()
            avg_5 = df5[col].mean()
            diff = avg_11 - avg_5
            ax.text(0.02, 0.98, f'平均差: {diff:+.1f}°C', 
                   transform=ax.transAxes, fontsize=9,
                   bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.7),
                   verticalalignment='top')
        
        plt.tight_layout()
        plt.subplots_adjust(top=0.93)
        plt.savefig('complete_comparison.png', dpi=300, bbox_inches='tight')
        plt.close()
        
        # 2. 关键参数对比图 (2x2)
        key_params = ['环境温度', '遮阳罩表面温度', '制冷帐篷表面温度', '皮革表面温度']
        
        fig2, axes = plt.subplots(2, 2, figsize=(16, 12))
        fig2.suptitle('关键温度参数对比 - 测试11 vs 测试5', fontsize=16, fontweight='bold')
        
        for i, param in enumerate(key_params):
            row = i // 2
            col = i % 2
            ax = axes[row, col]
            
            ax.plot(df11['时间']/60, df11[param], 
                   color=colors['11'], linewidth=3, alpha=0.9, 
                   label='测试11', marker='o', markersize=1)
            ax.plot(df5['时间']/60, df5[param], 
                   color=colors['5'], linewidth=3, alpha=0.9, 
                   label='测试5', marker='s', markersize=1)
            
            ax.set_title(param, fontsize=14, fontweight='bold')
            ax.set_xlabel('时间 (分钟)', fontsize=12)
            ax.set_ylabel('温度 (°C)', fontsize=12)
            ax.legend(fontsize=11)
            ax.grid(True, alpha=0.4)
            
            # 添加统计信息
            start_11 = df11[param].iloc[0]
            end_11 = df11[param].iloc[-1]
            start_5 = df5[param].iloc[0]
            end_5 = df5[param].iloc[-1]
            
            change_11 = end_11 - start_11
            change_5 = end_5 - start_5
            
            stats_text = f'测试11: {start_11:.1f}→{end_11:.1f}°C ({change_11:+.1f})\n'
            stats_text += f'测试5: {start_5:.1f}→{end_5:.1f}°C ({change_5:+.1f})'
            
            ax.text(0.02, 0.98, stats_text, transform=ax.transAxes, fontsize=10,
                   bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8),
                   verticalalignment='top')
        
        plt.tight_layout()
        plt.subplots_adjust(top=0.93)
        plt.savefig('key_parameters_comparison.png', dpi=300, bbox_inches='tight')
        plt.close()
        
        # 3. 制冷效果专项对比图
        fig3, (ax1, ax2) = plt.subplots(1, 2, figsize=(18, 8))
        fig3.suptitle('制冷效果专项对比分析', fontsize=16, fontweight='bold')
        
        # 左图：制冷帐篷温度
        ax1.plot(df11['时间']/60, df11['制冷帐篷表面温度'], 
                color=colors['11'], linewidth=3, label='测试11-表面', alpha=0.9)
        ax1.plot(df11['时间']/60, df11['制冷帐篷背面温度'], 
                color=colors['11'], linewidth=2, linestyle='--', label='测试11-背面', alpha=0.7)
        ax1.plot(df5['时间']/60, df5['制冷帐篷表面温度'], 
                color=colors['5'], linewidth=3, label='测试5-表面', alpha=0.9)
        ax1.plot(df5['时间']/60, df5['制冷帐篷背面温度'], 
                color=colors['5'], linewidth=2, linestyle='--', label='测试5-背面', alpha=0.7)
        
        ax1.set_title('制冷帐篷温度对比', fontsize=14, fontweight='bold')
        ax1.set_xlabel('时间 (分钟)', fontsize=12)
        ax1.set_ylabel('温度 (°C)', fontsize=12)
        ax1.legend(fontsize=10)
        ax1.grid(True, alpha=0.3)
        
        # 右图：皮革温度
        ax2.plot(df11['时间']/60, df11['皮革表面温度'], 
                color=colors['11'], linewidth=3, label='测试11-表面', alpha=0.9)
        ax2.plot(df11['时间']/60, df11['皮革背面温度'], 
                color=colors['11'], linewidth=2, linestyle='--', label='测试11-背面', alpha=0.7)
        ax2.plot(df5['时间']/60, df5['皮革表面温度'], 
                color=colors['5'], linewidth=3, label='测试5-表面', alpha=0.9)
        ax2.plot(df5['时间']/60, df5['皮革背面温度'], 
                color=colors['5'], linewidth=2, linestyle='--', label='测试5-背面', alpha=0.7)
        
        ax2.set_title('皮革温度对比', fontsize=14, fontweight='bold')
        ax2.set_xlabel('时间 (分钟)', fontsize=12)
        ax2.set_ylabel('温度 (°C)', fontsize=12)
        ax2.legend(fontsize=10)
        ax2.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.subplots_adjust(top=0.9)
        plt.savefig('cooling_effect_comparison.png', dpi=300, bbox_inches='tight')
        plt.close()
    
    # 生成对比图按钮
    compare_btn = tk.Button(button_frame, text="生成折线图对比", 
                           command=run_comparison, 
                           font=("Arial", 14, "bold"),
                           bg="#4CAF50", fg="white",
                           width=20, height=2)
    compare_btn.pack(pady=10)
    
    # 信息显示区域
    info_frame = tk.LabelFrame(root, text="数据预览", font=("Arial", 12, "bold"), 
                              fg="blue", padx=10, pady=10)
    info_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
    
    info_text = tk.Text(info_frame, height=15, wrap=tk.WORD, font=("Consolas", 10))
    scrollbar = tk.Scrollbar(info_frame, orient=tk.VERTICAL, command=info_text.yview)
    info_text.configure(yscrollcommand=scrollbar.set)
    
    info_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
    scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    # 显示数据预览
    try:
        import pandas as pd
        df11 = pd.read_csv('11_fixed_headers.csv')
        df5 = pd.read_csv('5_fixed_headers.csv')
        
        info_content = "=== 测试结果对比分析 ===\n\n"
        info_content += f"📊 数据规模对比:\n"
        info_content += f"   测试11: {len(df11):,} 个数据点\n"
        info_content += f"   测试5:  {len(df5):,} 个数据点\n"
        info_content += f"   差值:   {len(df5) - len(df11):,} 个数据点\n\n"
        
        info_content += f"⏱️ 测试时长对比:\n"
        info_content += f"   测试11: {df11['时间'].max():.1f} 秒 ({df11['时间'].max()/60:.1f} 分钟)\n"
        info_content += f"   测试5:  {df5['时间'].max():.1f} 秒 ({df5['时间'].max()/60:.1f} 分钟)\n"
        info_content += f"   差值:   {(df5['时间'].max() - df11['时间'].max())/60:.1f} 分钟\n\n"
        
        info_content += f"🌡️ 关键温度对比 (平均值):\n"
        
        # 环境温度
        env_11 = df11['环境温度'].mean()
        env_5 = df5['环境温度'].mean()
        info_content += f"   环境温度:     测试11={env_11:.1f}°C, 测试5={env_5:.1f}°C, 差值={env_11-env_5:+.1f}°C\n"
        
        # 制冷效果
        cool_11 = df11['制冷帐篷表面温度'].mean()
        cool_5 = df5['制冷帐篷表面温度'].mean()
        info_content += f"   制冷帐篷表面: 测试11={cool_11:.1f}°C, 测试5={cool_5:.1f}°C, 差值={cool_11-cool_5:+.1f}°C\n"
        
        # 皮革温度
        leather_11 = df11['皮革表面温度'].mean()
        leather_5 = df5['皮革表面温度'].mean()
        info_content += f"   皮革表面:     测试11={leather_11:.1f}°C, 测试5={leather_5:.1f}°C, 差值={leather_11-leather_5:+.1f}°C\n\n"
        
        info_content += "🎯 主要发现:\n"
        if cool_5 < cool_11:
            info_content += "   • 测试5的制冷效果更好\n"
        if leather_5 < leather_11:
            info_content += "   • 测试5的皮革温度控制更优\n"
        if len(df5) > len(df11):
            info_content += "   • 测试5的测试时间更长，数据更充分\n"
        
        info_content += "\n点击上方按钮生成详细的折线图对比分析。"
        
        info_text.insert(tk.END, info_content)
        
    except Exception as e:
        info_text.insert(tk.END, f"数据预览失败: {e}\n\n请确保CSV文件格式正确。")
    
    root.mainloop()

def main():
    """主函数"""
    print("测试11 vs 测试5 对比分析工具")
    print("=" * 50)
    
    # 检查文件
    missing_files = check_files()
    if missing_files:
        print(f"错误：缺少以下文件：")
        for file in missing_files:
            print(f"  - {file}")
        print("\n请确保所有必要文件都在当前目录中。")
        input("按回车键退出...")
        return
    
    print("所有必要文件检查完成。")
    print("正在启动对比分析界面...")
    
    # 创建对比界面
    create_comparison_interface()

if __name__ == "__main__":
    main()
