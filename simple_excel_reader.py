#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的Excel文件读取器
尝试多种方法读取Excel文件
"""

import pandas as pd
import os

def try_read_excel(filename):
    """尝试多种方法读取Excel文件"""
    print(f"尝试读取文件: {filename}")
    
    if not os.path.exists(filename):
        print(f"  文件不存在: {filename}")
        return None
    
    # 显示文件信息
    file_size = os.path.getsize(filename)
    print(f"  文件大小: {file_size:,} 字节")
    
    methods = [
        ("默认方法", lambda f: pd.read_excel(f)),
        ("openpyxl引擎", lambda f: pd.read_excel(f, engine='openpyxl')),
        ("无头部", lambda f: pd.read_excel(f, header=None)),
        ("跳过行", lambda f: pd.read_excel(f, skiprows=1)),
        ("指定工作表", lambda f: pd.read_excel(f, sheet_name=0)),
    ]
    
    for method_name, method_func in methods:
        try:
            print(f"  尝试方法: {method_name}")
            df = method_func(filename)
            print(f"    成功! 数据形状: {df.shape}")
            print(f"    列数: {len(df.columns)}")
            print(f"    前几列: {list(df.columns[:5])}")
            
            # 显示前几行数据
            print("    前3行数据:")
            print(df.head(3).to_string(max_cols=5))
            print()
            
            return df
            
        except Exception as e:
            print(f"    失败: {str(e)[:100]}...")
    
    print(f"  所有方法都失败了")
    return None

def main():
    """主函数"""
    print("Excel文件读取测试")
    print("=" * 50)
    
    files_to_test = ['12.xlsx', '13.xlsx']
    
    for filename in files_to_test:
        df = try_read_excel(filename)
        if df is not None:
            # 尝试保存为CSV
            csv_filename = filename.replace('.xlsx', '_simple.csv')
            try:
                df.to_csv(csv_filename, index=False, encoding='utf-8-sig')
                print(f"  成功保存为: {csv_filename}")
            except Exception as e:
                print(f"  保存CSV失败: {e}")
        
        print("-" * 50)

if __name__ == "__main__":
    main()
