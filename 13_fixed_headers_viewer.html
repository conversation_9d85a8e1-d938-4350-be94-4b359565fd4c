
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>13_fixed_headers.csv - 数据查看器</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }
        .info {
            background-color: #e8f4fd;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        tr:nth-child(even) {
            background-color: #f8f9fa;
        }
        .stats {
            margin-top: 30px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>13_fixed_headers.csv 数据查看器</h1>
        
        <div class="info">
            <h3>文件信息</h3>
            <p><strong>数据形状:</strong> 2852 行 x 11 列</p>
            <p><strong>列名:</strong> Time, 时间, 遮阳罩表面温度, 遮阳罩背面温度, 遮阳罩皮革表面温度, 制冷帐篷表面温度, 制冷帐篷背面温度, 制冷帐篷皮革温度, 皮革表面温度, 皮革背面温度, 环境温度</p>
        </div>
        
        <h3>数据预览 (前20行)</h3>
        <table border="1" class="dataframe table" id="data-table">
  <thead>
    <tr style="text-align: right;">
      <th></th>
      <th>Time</th>
      <th>时间</th>
      <th>遮阳罩表面温度</th>
      <th>遮阳罩背面温度</th>
      <th>遮阳罩皮革表面温度</th>
      <th>制冷帐篷表面温度</th>
      <th>制冷帐篷背面温度</th>
      <th>制冷帐篷皮革温度</th>
      <th>皮革表面温度</th>
      <th>皮革背面温度</th>
      <th>环境温度</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <th>0</th>
      <td>44383.396989</td>
      <td>0.000000</td>
      <td>38.500000</td>
      <td>37.086535</td>
      <td>33.713134</td>
      <td>32.981964</td>
      <td>33.415692</td>
      <td>33.593252</td>
      <td>34.912129</td>
      <td>34.383676</td>
      <td>33.261447</td>
    </tr>
    <tr>
      <th>1</th>
      <td>0.000000</td>
      <td>9.999970</td>
      <td>38.499403</td>
      <td>37.349044</td>
      <td>33.847461</td>
      <td>32.114024</td>
      <td>33.790045</td>
      <td>33.741548</td>
      <td>35.305376</td>
      <td>34.519976</td>
      <td>33.584148</td>
    </tr>
    <tr>
      <th>2</th>
      <td>0.000000</td>
      <td>19.999978</td>
      <td>38.636157</td>
      <td>37.361459</td>
      <td>33.996692</td>
      <td>32.949149</td>
      <td>33.717173</td>
      <td>33.968103</td>
      <td>35.039554</td>
      <td>34.639399</td>
      <td>33.005923</td>
    </tr>
    <tr>
      <th>3</th>
      <td>0.000000</td>
      <td>29.999884</td>
      <td>38.595852</td>
      <td>37.260000</td>
      <td>34.023685</td>
      <td>31.461414</td>
      <td>32.831934</td>
      <td>33.977486</td>
      <td>35.225531</td>
      <td>34.567403</td>
      <td>32.777669</td>
    </tr>
    <tr>
      <th>4</th>
      <td>0.000000</td>
      <td>40.000532</td>
      <td>37.846359</td>
      <td>36.262686</td>
      <td>33.690229</td>
      <td>31.312891</td>
      <td>31.839893</td>
      <td>33.721342</td>
      <td>35.151100</td>
      <td>34.113378</td>
      <td>31.590064</td>
    </tr>
    <tr>
      <th>5</th>
      <td>0.000000</td>
      <td>50.001388</td>
      <td>37.545216</td>
      <td>35.993982</td>
      <td>33.699334</td>
      <td>33.608736</td>
      <td>32.211043</td>
      <td>33.697504</td>
      <td>35.078858</td>
      <td>34.005388</td>
      <td>32.378726</td>
    </tr>
    <tr>
      <th>6</th>
      <td>0.000000</td>
      <td>60.001754</td>
      <td>37.955326</td>
      <td>36.515113</td>
      <td>33.825005</td>
      <td>31.513076</td>
      <td>32.549671</td>
      <td>33.752719</td>
      <td>34.982746</td>
      <td>34.131480</td>
      <td>32.219602</td>
    </tr>
    <tr>
      <th>7</th>
      <td>0.000000</td>
      <td>70.002026</td>
      <td>37.883256</td>
      <td>36.330217</td>
      <td>33.807333</td>
      <td>32.779524</td>
      <td>32.999084</td>
      <td>33.666185</td>
      <td>34.952069</td>
      <td>34.189048</td>
      <td>32.379437</td>
    </tr>
    <tr>
      <th>8</th>
      <td>0.000000</td>
      <td>80.001786</td>
      <td>38.348279</td>
      <td>36.987269</td>
      <td>34.084305</td>
      <td>32.530138</td>
      <td>32.810198</td>
      <td>33.907055</td>
      <td>35.162722</td>
      <td>34.503183</td>
      <td>32.830575</td>
    </tr>
    <tr>
      <th>9</th>
      <td>0.000000</td>
      <td>90.002601</td>
      <td>38.759077</td>
      <td>37.340203</td>
      <td>34.218007</td>
      <td>33.714605</td>
      <td>32.178454</td>
      <td>33.983375</td>
      <td>35.144600</td>
      <td>34.663576</td>
      <td>32.160813</td>
    </tr>
    <tr>
      <th>10</th>
      <td>0.000000</td>
      <td>100.003065</td>
      <td>38.061018</td>
      <td>36.364012</td>
      <td>34.009549</td>
      <td>32.020693</td>
      <td>32.261028</td>
      <td>33.836633</td>
      <td>34.779097</td>
      <td>34.140361</td>
      <td>31.817890</td>
    </tr>
    <tr>
      <th>11</th>
      <td>0.000000</td>
      <td>110.003263</td>
      <td>38.026409</td>
      <td>36.437291</td>
      <td>33.990592</td>
      <td>34.158452</td>
      <td>32.098164</td>
      <td>33.820647</td>
      <td>34.174459</td>
      <td>33.375642</td>
      <td>31.782622</td>
    </tr>
    <tr>
      <th>12</th>
      <td>0.000000</td>
      <td>120.003495</td>
      <td>37.969918</td>
      <td>36.440049</td>
      <td>33.881284</td>
      <td>34.832621</td>
      <td>31.419549</td>
      <td>33.776290</td>
      <td>33.603565</td>
      <td>32.805922</td>
      <td>31.919458</td>
    </tr>
    <tr>
      <th>13</th>
      <td>0.000000</td>
      <td>130.003549</td>
      <td>38.517466</td>
      <td>37.027812</td>
      <td>34.116268</td>
      <td>35.058028</td>
      <td>32.916607</td>
      <td>33.884586</td>
      <td>34.078992</td>
      <td>33.369813</td>
      <td>32.811518</td>
    </tr>
    <tr>
      <th>14</th>
      <td>0.000000</td>
      <td>140.004543</td>
      <td>38.600463</td>
      <td>37.047521</td>
      <td>34.252192</td>
      <td>35.304171</td>
      <td>32.969128</td>
      <td>33.981888</td>
      <td>33.986462</td>
      <td>33.478603</td>
      <td>32.698957</td>
    </tr>
    <tr>
      <th>15</th>
      <td>0.000000</td>
      <td>150.004754</td>
      <td>38.492895</td>
      <td>37.085540</td>
      <td>34.294882</td>
      <td>34.081993</td>
      <td>32.165901</td>
      <td>34.015899</td>
      <td>33.864257</td>
      <td>33.243055</td>
      <td>32.765981</td>
    </tr>
    <tr>
      <th>16</th>
      <td>0.000000</td>
      <td>160.004538</td>
      <td>38.438268</td>
      <td>36.721996</td>
      <td>34.107873</td>
      <td>34.893118</td>
      <td>32.160670</td>
      <td>33.892887</td>
      <td>33.912100</td>
      <td>33.229818</td>
      <td>32.676716</td>
    </tr>
    <tr>
      <th>17</th>
      <td>0.000000</td>
      <td>170.005132</td>
      <td>38.096731</td>
      <td>36.492882</td>
      <td>34.248689</td>
      <td>36.016566</td>
      <td>33.454733</td>
      <td>33.960773</td>
      <td>33.997367</td>
      <td>33.546945</td>
      <td>32.947993</td>
    </tr>
    <tr>
      <th>18</th>
      <td>0.000000</td>
      <td>180.004920</td>
      <td>38.044971</td>
      <td>36.610123</td>
      <td>34.388807</td>
      <td>36.337214</td>
      <td>33.299540</td>
      <td>34.037353</td>
      <td>34.146895</td>
      <td>33.620809</td>
      <td>33.085536</td>
    </tr>
    <tr>
      <th>19</th>
      <td>0.000000</td>
      <td>190.004801</td>
      <td>37.991447</td>
      <td>36.274022</td>
      <td>34.078085</td>
      <td>37.022803</td>
      <td>32.173900</td>
      <td>33.812999</td>
      <td>34.665204</td>
      <td>33.586057</td>
      <td>32.069654</td>
    </tr>
  </tbody>
</table>
        
        <div class="stats">
            <h3>数值列统计</h3>
            <table border="1" class="dataframe table">
  <thead>
    <tr style="text-align: right;">
      <th></th>
      <th>Time</th>
      <th>时间</th>
      <th>遮阳罩表面温度</th>
      <th>遮阳罩背面温度</th>
      <th>遮阳罩皮革表面温度</th>
      <th>制冷帐篷表面温度</th>
      <th>制冷帐篷背面温度</th>
      <th>制冷帐篷皮革温度</th>
      <th>皮革表面温度</th>
      <th>皮革背面温度</th>
      <th>环境温度</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <th>count</th>
      <td>2852.000000</td>
      <td>2852.000000</td>
      <td>2852.000000</td>
      <td>2852.000000</td>
      <td>2852.000000</td>
      <td>2852.000000</td>
      <td>2852.000000</td>
      <td>2852.000000</td>
      <td>2852.000000</td>
      <td>2852.000000</td>
      <td>2852.000000</td>
    </tr>
    <tr>
      <th>mean</th>
      <td>15.562201</td>
      <td>14255.518904</td>
      <td>49.187486</td>
      <td>47.650994</td>
      <td>40.104249</td>
      <td>45.194012</td>
      <td>40.733474</td>
      <td>38.293691</td>
      <td>42.096665</td>
      <td>45.360816</td>
      <td>36.458667</td>
    </tr>
    <tr>
      <th>std</th>
      <td>831.085640</td>
      <td>8234.759609</td>
      <td>8.124379</td>
      <td>7.369140</td>
      <td>4.364014</td>
      <td>4.899361</td>
      <td>3.729959</td>
      <td>3.111458</td>
      <td>4.489924</td>
      <td>4.766287</td>
      <td>2.877649</td>
    </tr>
    <tr>
      <th>min</th>
      <td>0.000000</td>
      <td>0.000000</td>
      <td>33.920225</td>
      <td>35.113452</td>
      <td>32.930776</td>
      <td>31.312891</td>
      <td>31.415922</td>
      <td>32.983011</td>
      <td>33.603565</td>
      <td>31.146215</td>
      <td>30.448929</td>
    </tr>
    <tr>
      <th>25%</th>
      <td>0.000000</td>
      <td>7127.742881</td>
      <td>42.070419</td>
      <td>40.669795</td>
      <td>36.397806</td>
      <td>41.915766</td>
      <td>38.370440</td>
      <td>35.265633</td>
      <td>38.522494</td>
      <td>41.864334</td>
      <td>34.330707</td>
    </tr>
    <tr>
      <th>50%</th>
      <td>0.000000</td>
      <td>14255.517681</td>
      <td>49.199122</td>
      <td>46.957670</td>
      <td>39.633997</td>
      <td>46.102882</td>
      <td>40.983856</td>
      <td>39.098014</td>
      <td>41.228216</td>
      <td>46.005045</td>
      <td>36.817308</td>
    </tr>
    <tr>
      <th>75%</th>
      <td>0.000000</td>
      <td>21383.294863</td>
      <td>57.176239</td>
      <td>53.536311</td>
      <td>42.156053</td>
      <td>49.363275</td>
      <td>43.896805</td>
      <td>40.783337</td>
      <td>45.012915</td>
      <td>48.635137</td>
      <td>38.126986</td>
    </tr>
    <tr>
      <th>max</th>
      <td>44383.396989</td>
      <td>28511.047375</td>
      <td>63.312067</td>
      <td>66.987752</td>
      <td>53.880308</td>
      <td>53.824334</td>
      <td>48.158651</td>
      <td>46.577860</td>
      <td>53.870362</td>
      <td>57.165601</td>
      <td>45.682026</td>
    </tr>
  </tbody>
</table>
        </div>
    </div>
</body>
</html>
