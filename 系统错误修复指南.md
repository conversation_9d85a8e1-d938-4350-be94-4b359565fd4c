# 系统错误修复指南

## 问题描述
当尝试打开文件时出现错误：
```
An error occurred opening an external program.
Failed to open: 系统找不到指定的文件。(0x2)
```

## 🔧 解决方案

### 方案1：使用生成的HTML查看器 ⭐ 推荐
我已经为每个CSV文件创建了HTML查看器：
- `5_fixed_headers_viewer.html`
- `11_fixed_headers_viewer.html`
- `12_fixed_headers_viewer.html`
- `13_fixed_headers_viewer.html`

**使用方法：**
1. 双击任意HTML文件
2. 在浏览器中查看数据
3. 包含完整的数据预览和统计信息

### 方案2：修复文件关联
1. **右键点击CSV文件**
2. **选择"打开方式"**
3. **选择"选择其他应用"**
4. **选择Excel、记事本或其他文本编辑器**
5. **勾选"始终使用此应用打开.csv文件"**

### 方案3：使用记事本打开
1. **打开记事本**
2. **文件 → 打开**
3. **文件类型选择"所有文件(*.*)"**
4. **找到并打开CSV文件**

### 方案4：使用Excel打开
1. **打开Excel**
2. **文件 → 打开**
3. **选择CSV文件**
4. **按照导入向导操作**

## 📊 数据概览

### 测试13数据信息
- **数据行数：** 3,129 行
- **数据列数：** 11 列
- **测试时长：** 475.2 分钟
- **异常点：** 已修复（0分钟和385分钟附近）

### 列名说明
1. `Time` - 原始时间戳
2. `时间` - 转换后的时间（秒）
3. `遮阳罩表面温度` - 遮阳罩表面温度
4. `遮阳罩背面温度` - 遮阳罩背面温度
5. `遮阳罩皮革表面温度` - 遮阳罩皮革表面温度（已修复异常点）
6. `制冷帐篷表面温度` - 制冷帐篷表面温度
7. `制冷帐篷背面温度` - 制冷帐篷背面温度
8. `制冷帐篷皮革温度` - 制冷帐篷皮革温度
9. `皮革表面温度` - 皮革表面温度
10. `皮革背面温度` - 皮革背面温度
11. `环境温度` - 环境温度

## 🎯 关键数据

### 制冷效果统计
- **平均制冷帐篷表面温度：** 37.49°C
- **最低制冷帐篷表面温度：** 32.52°C
- **最高制冷帐篷表面温度：** 50.04°C
- **温度变化范围：** 17.52°C

### 环境条件
- **平均环境温度：** 33.30°C
- **环境温度范围：** 30.25°C - 36.46°C

## 💡 使用建议

1. **优先使用HTML查看器** - 最简单可靠的方法
2. **如需编辑数据** - 使用Excel打开CSV文件
3. **如需查看原始数据** - 使用记事本打开
4. **避免直接双击CSV文件** - 可能触发系统错误

## 🔍 故障排除

如果仍然遇到问题：

1. **检查文件路径** - 确保路径中没有特殊字符
2. **检查文件权限** - 确保有读取权限
3. **重启应用程序** - 重启VS Code或相关程序
4. **使用管理员权限** - 以管理员身份运行程序

## 📁 相关文件

- `csv_viewer.py` - CSV查看器脚本
- `*_viewer.html` - 各个数据文件的HTML查看器
- `修复版_*.png` - 生成的折线图文件

---

**注意：** 这个错误通常是Windows系统的文件关联问题，不影响数据的完整性和分析结果。
