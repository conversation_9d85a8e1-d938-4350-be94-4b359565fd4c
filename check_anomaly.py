#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查遮阳罩皮革表面温度异常点
"""

import pandas as pd
import matplotlib.pyplot as plt
import matplotlib
import numpy as np
import os

# 设置matplotlib后端和中文字体
matplotlib.use('Agg')
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def check_anomaly_in_data():
    """检查遮阳罩皮革表面温度的异常点"""
    print("正在检查遮阳罩皮革表面温度异常点...")
    
    # 文件映射
    files = {
        '5': '5_fixed_headers.csv',
        '11': '11_fixed_headers.csv',
        '12': '12_fixed_headers.csv',
        '13': '13_fixed_headers.csv'
    }
    
    data = {}
    
    # 加载所有数据
    for test_id, filename in files.items():
        if os.path.exists(filename):
            try:
                df = pd.read_csv(filename)
                data[test_id] = df
                print(f"✓ 测试{test_id}: {len(df)} 个数据点")
            except Exception as e:
                print(f"✗ 测试{test_id}: 加载失败 - {e}")
    
    # 检查遮阳罩皮革表面温度
    param = '遮阳罩皮革表面温度'
    print(f"\n正在分析参数: {param}")
    print("=" * 60)
    
    anomalies_found = {}
    
    for test_id, df in data.items():
        if param in df.columns:
            values = df[param]
            times = df['时间'] / 60  # 转换为分钟
            
            # 基本统计
            mean_val = values.mean()
            std_val = values.std()
            min_val = values.min()
            max_val = values.max()
            
            print(f"\n测试{test_id} - {param}:")
            print(f"  平均值: {mean_val:.2f}°C")
            print(f"  标准差: {std_val:.2f}°C")
            print(f"  最小值: {min_val:.2f}°C")
            print(f"  最大值: {max_val:.2f}°C")
            
            # 检查异常值 (使用3倍标准差规则)
            threshold = 3 * std_val
            anomaly_mask = np.abs(values - mean_val) > threshold
            anomaly_indices = df.index[anomaly_mask]
            
            if len(anomaly_indices) > 0:
                print(f"  发现 {len(anomaly_indices)} 个异常点 (超过3倍标准差):")
                anomalies_found[test_id] = []
                
                for idx in anomaly_indices:
                    time_val = times.iloc[idx]
                    temp_val = values.iloc[idx]
                    deviation = abs(temp_val - mean_val)
                    print(f"    时间: {time_val:.1f}分钟, 温度: {temp_val:.2f}°C, 偏差: {deviation:.2f}°C")
                    anomalies_found[test_id].append({
                        'time': time_val,
                        'temperature': temp_val,
                        'deviation': deviation,
                        'index': idx
                    })
            
            # 检查极端值 (超出合理范围)
            reasonable_min = 0   # 0°C
            reasonable_max = 100 # 100°C
            extreme_mask = (values < reasonable_min) | (values > reasonable_max)
            extreme_indices = df.index[extreme_mask]
            
            if len(extreme_indices) > 0:
                print(f"  发现 {len(extreme_indices)} 个极端值 (超出0-100°C范围):")
                for idx in extreme_indices:
                    time_val = times.iloc[idx]
                    temp_val = values.iloc[idx]
                    print(f"    时间: {time_val:.1f}分钟, 温度: {temp_val:.2f}°C")
            
            # 检查突变点 (相邻点温差过大)
            diff = np.abs(np.diff(values))
            sudden_change_threshold = 10  # 10°C
            sudden_change_mask = diff > sudden_change_threshold
            sudden_change_indices = np.where(sudden_change_mask)[0]
            
            if len(sudden_change_indices) > 0:
                print(f"  发现 {len(sudden_change_indices)} 个突变点 (相邻点温差>10°C):")
                for idx in sudden_change_indices:
                    time_before = times.iloc[idx]
                    time_after = times.iloc[idx + 1]
                    temp_before = values.iloc[idx]
                    temp_after = values.iloc[idx + 1]
                    temp_diff = abs(temp_after - temp_before)
                    print(f"    时间: {time_before:.1f}→{time_after:.1f}分钟, 温度: {temp_before:.2f}→{temp_after:.2f}°C, 变化: {temp_diff:.2f}°C")
    
    # 创建异常点可视化图表
    create_anomaly_visualization(data, param, anomalies_found)
    
    return anomalies_found

def create_anomaly_visualization(data, param, anomalies_found):
    """创建异常点可视化图表"""
    print(f"\n正在生成{param}异常点可视化图表...")
    
    # 颜色配置
    colors = {
        '5': '#4ECDC4',   # 青绿色
        '11': '#FF6B6B',  # 红色
        '12': '#45B7D1',  # 蓝色
        '13': '#FFA07A'   # 橙色
    }
    
    # 创建图表
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(16, 12))
    fig.suptitle(f'{param} - 异常点检测分析', fontsize=16, fontweight='bold')
    
    # 上图：完整时间序列
    max_time = 0
    for test_id, df in data.items():
        if param in df.columns:
            times = df['时间'] / 60
            values = df[param]
            max_time = max(max_time, times.max())
            
            # 绘制正常数据点
            ax1.plot(times, values, color=colors[test_id], linewidth=2, 
                    alpha=0.7, label=f'测试{test_id}')
            
            # 标记异常点
            if test_id in anomalies_found:
                for anomaly in anomalies_found[test_id]:
                    ax1.scatter(anomaly['time'], anomaly['temperature'], 
                              color='red', s=100, marker='x', linewidth=3,
                              zorder=5)
                    ax1.annotate(f'{anomaly["temperature"]:.1f}°C', 
                               (anomaly['time'], anomaly['temperature']),
                               xytext=(5, 5), textcoords='offset points',
                               fontsize=8, color='red', fontweight='bold')
    
    ax1.set_xlim(0, max_time)
    ax1.set_title(f'{param} - 完整时间序列 (红色X标记异常点)', fontsize=14)
    ax1.set_xlabel('时间 (分钟)', fontsize=12)
    ax1.set_ylabel('温度 (°C)', fontsize=12)
    ax1.legend(fontsize=10)
    ax1.grid(True, alpha=0.3)
    
    # 下图：局部放大显示异常区域
    if anomalies_found:
        # 找到所有异常点的时间范围
        all_anomaly_times = []
        for test_anomalies in anomalies_found.values():
            for anomaly in test_anomalies:
                all_anomaly_times.append(anomaly['time'])
        
        if all_anomaly_times:
            min_anomaly_time = min(all_anomaly_times)
            max_anomaly_time = max(all_anomaly_times)
            
            # 扩展显示范围
            time_margin = max(10, (max_anomaly_time - min_anomaly_time) * 0.2)
            zoom_start = max(0, min_anomaly_time - time_margin)
            zoom_end = min(max_time, max_anomaly_time + time_margin)
            
            for test_id, df in data.items():
                if param in df.columns:
                    times = df['时间'] / 60
                    values = df[param]
                    
                    # 只显示放大区域的数据
                    mask = (times >= zoom_start) & (times <= zoom_end)
                    if mask.any():
                        ax2.plot(times[mask], values[mask], color=colors[test_id], 
                               linewidth=3, alpha=0.8, label=f'测试{test_id}',
                               marker='o', markersize=3)
                    
                    # 标记异常点
                    if test_id in anomalies_found:
                        for anomaly in anomalies_found[test_id]:
                            if zoom_start <= anomaly['time'] <= zoom_end:
                                ax2.scatter(anomaly['time'], anomaly['temperature'], 
                                          color='red', s=150, marker='x', linewidth=4,
                                          zorder=5)
                                ax2.annotate(f'{anomaly["temperature"]:.1f}°C\n({anomaly["time"]:.1f}min)', 
                                           (anomaly['time'], anomaly['temperature']),
                                           xytext=(10, 10), textcoords='offset points',
                                           fontsize=10, color='red', fontweight='bold',
                                           bbox=dict(boxstyle='round,pad=0.3', 
                                                   facecolor='yellow', alpha=0.7))
            
            ax2.set_xlim(zoom_start, zoom_end)
            ax2.set_title(f'{param} - 异常区域放大显示 ({zoom_start:.1f}-{zoom_end:.1f}分钟)', fontsize=14)
            ax2.set_xlabel('时间 (分钟)', fontsize=12)
            ax2.set_ylabel('温度 (°C)', fontsize=12)
            ax2.legend(fontsize=10)
            ax2.grid(True, alpha=0.3)
        else:
            ax2.text(0.5, 0.5, '未发现异常点', transform=ax2.transAxes, 
                    fontsize=16, ha='center', va='center')
            ax2.set_title('异常点分析 - 未发现异常', fontsize=14)
    else:
        ax2.text(0.5, 0.5, '未发现异常点', transform=ax2.transAxes, 
                fontsize=16, ha='center', va='center')
        ax2.set_title('异常点分析 - 未发现异常', fontsize=14)
    
    plt.tight_layout()
    plt.savefig('遮阳罩皮革表面温度异常点分析.png', dpi=300, bbox_inches='tight')
    print("✓ 异常点分析图表已保存: 遮阳罩皮革表面温度异常点分析.png")
    plt.close()

def main():
    """主函数"""
    print("遮阳罩皮革表面温度异常点检测工具")
    print("=" * 60)
    
    anomalies = check_anomaly_in_data()
    
    print("\n" + "=" * 60)
    print("异常点检测总结:")
    
    if anomalies:
        total_anomalies = sum(len(test_anomalies) for test_anomalies in anomalies.values())
        print(f"总共发现 {total_anomalies} 个异常点")
        
        for test_id, test_anomalies in anomalies.items():
            if test_anomalies:
                print(f"\n测试{test_id}: {len(test_anomalies)} 个异常点")
                for i, anomaly in enumerate(test_anomalies, 1):
                    print(f"  {i}. 时间: {anomaly['time']:.1f}分钟, "
                          f"温度: {anomaly['temperature']:.2f}°C, "
                          f"偏差: {anomaly['deviation']:.2f}°C")
    else:
        print("未发现明显异常点")
    
    print("\n异常点分析图表已生成，请查看PNG文件。")

if __name__ == "__main__":
    main()
