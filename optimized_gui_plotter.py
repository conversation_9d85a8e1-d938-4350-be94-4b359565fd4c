#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优化版数据可视化程序 - 完整功能GUI版本
重写优化版本，包含所有核心功能，界面更友好，性能更优
版本: 2.0 - 全面重构优化版
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, colorchooser
import os
import sys
import threading
import tempfile
import shutil
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 依赖包检查和导入
class DependencyManager:
    """依赖管理器 - 智能检测和处理依赖包"""
    
    def __init__(self):
        self.dependencies = {
            'matplotlib': {'available': False, 'error': None, 'module': None},
            'pandas': {'available': False, 'error': None, 'module': None},
            'numpy': {'available': False, 'error': None, 'module': None},
            'openpyxl': {'available': False, 'error': None, 'module': None}
        }
        self.check_all_dependencies()
    
    def check_all_dependencies(self):
        """检查所有依赖包"""
        # 检查matplotlib
        try:
            import matplotlib.pyplot as plt
            from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg, NavigationToolbar2Tk
            import matplotlib.dates as mdates
            self.dependencies['matplotlib']['available'] = True
            self.dependencies['matplotlib']['module'] = {
                'plt': plt, 'FigureCanvasTkAgg': FigureCanvasTkAgg, 
                'NavigationToolbar2Tk': NavigationToolbar2Tk, 'mdates': mdates
            }
        except ImportError as e:
            self.dependencies['matplotlib']['error'] = str(e)
        
        # 检查pandas
        try:
            import pandas as pd
            self.dependencies['pandas']['available'] = True
            self.dependencies['pandas']['module'] = pd
        except ImportError as e:
            self.dependencies['pandas']['error'] = str(e)
        
        # 检查numpy
        try:
            import numpy as np
            self.dependencies['numpy']['available'] = True
            self.dependencies['numpy']['module'] = np
        except ImportError as e:
            self.dependencies['numpy']['error'] = str(e)
        
        # 检查openpyxl
        try:
            import openpyxl
            self.dependencies['openpyxl']['available'] = True
            self.dependencies['openpyxl']['module'] = openpyxl
        except ImportError as e:
            self.dependencies['openpyxl']['error'] = str(e)
    
    def get_missing_dependencies(self):
        """获取缺失的依赖包列表"""
        return [name for name, info in self.dependencies.items() if not info['available']]
    
    def all_dependencies_available(self):
        """检查是否所有依赖都可用"""
        return all(info['available'] for info in self.dependencies.values())
    
    def get_module(self, name):
        """获取已导入的模块"""
        if self.dependencies[name]['available']:
            return self.dependencies[name]['module']
        return None


class LayoutManager:
    """布局管理器 - 处理响应式布局和UI组件管理"""
    
    def __init__(self, parent):
        self.parent = parent
        self.current_layout_mode = 'wide'
        self.last_window_width = 0
        self.columns_per_row = 4
    
    def determine_layout_mode(self, width):
        """根据窗口宽度确定布局模式"""
        for mode, config in self.parent.layout_config.items():
            if width < config['breakpoint']:
                return mode
        return 'ultra_wide'
    
    def get_columns_per_row(self, layout_mode):
        """获取当前布局模式下每行显示的列数"""
        return self.parent.layout_config.get(layout_mode, {}).get('columns_per_row', 4)
    
    def update_layout(self, force=False):
        """更新布局"""
        current_width = self.parent.root.winfo_width()

        if abs(current_width - self.last_window_width) > 50 or force:
            new_layout_mode = self.determine_layout_mode(current_width)

            if new_layout_mode != self.current_layout_mode or force:
                self.current_layout_mode = new_layout_mode
                self.columns_per_row = self.get_columns_per_row(new_layout_mode)
                self.parent.update_column_selection_layout()

            self.last_window_width = current_width

    def calculate_column_frame_width(self):
        """计算数据列选择框的最佳宽度"""
        try:
            # 获取可用宽度
            if hasattr(self.parent, 'columns_canvas'):
                available_width = self.parent.columns_canvas.winfo_width()
            else:
                # 估算可用宽度（左侧面板宽度 - 边距）
                available_width = 380

            # 减去滚动条和边距
            scrollbar_width = 20
            margins_and_padding = 20
            usable_width = available_width - scrollbar_width - margins_and_padding

            # 计算每列的宽度
            columns_per_row = max(1, self.columns_per_row)
            column_spacing = 4 * (columns_per_row - 1)  # 列间距
            column_width = (usable_width - column_spacing) // columns_per_row

            # 设置最小和最大宽度限制
            min_width = 120  # 最小宽度，确保内容可读
            max_width = 250  # 最大宽度，避免过宽

            column_width = max(min_width, min(max_width, column_width))

            return column_width

        except Exception as e:
            print(f"计算列宽度失败: {e}")
            return 150  # 默认宽度

    def get_responsive_element_config(self):
        """获取响应式元素配置"""
        column_width = self.calculate_column_frame_width()

        # 根据列宽度调整元素配置
        if column_width < 140:
            # 超窄模式
            return {
                'checkbox_text_length': 8,
                'entry_font_size': 7,
                'color_button_size': 2,
                'layout_mode': 'compact'
            }
        elif column_width < 180:
            # 窄模式
            return {
                'checkbox_text_length': 12,
                'entry_font_size': 8,
                'color_button_size': 3,
                'layout_mode': 'normal'
            }
        else:
            # 宽模式
            return {
                'checkbox_text_length': 18,
                'entry_font_size': 9,
                'color_button_size': 4,
                'layout_mode': 'wide'
            }


class DataProcessor:
    """数据处理器 - 处理文件读取、数据清理和类型转换"""
    
    def __init__(self, parent):
        self.parent = parent
    
    def load_file(self, file_path):
        """加载文件 - 支持CSV和Excel"""
        try:
            file_ext = os.path.splitext(file_path)[1].lower()
            
            if file_ext == '.csv':
                return self.load_csv(file_path)
            elif file_ext in ['.xlsx', '.xls']:
                return self.load_excel(file_path)
            else:
                raise ValueError(f"不支持的文件格式: {file_ext}")
        
        except Exception as e:
            raise Exception(f"文件加载失败: {str(e)}")
    
    def load_csv(self, file_path):
        """加载CSV文件"""
        encodings = ['utf-8', 'gbk', 'gb2312', 'utf-8-sig', 'latin1']
        
        for encoding in encodings:
            try:
                data = self.parent.pd.read_csv(
                    file_path, 
                    encoding=encoding,
                    keep_default_na=True, 
                    na_values=['', 'NA', 'N/A', 'null', '#N/A']
                )
                return data, ["CSV数据"]
            except UnicodeDecodeError:
                continue
            except Exception as e:
                if encoding == encodings[-1]:  # 最后一个编码也失败
                    raise e
                continue
        
        raise ValueError("无法识别文件编码")
    
    def load_excel(self, file_path):
        """加载Excel文件 - 增强兼容性"""
        # 获取工作表名称
        sheet_names = self.get_sheet_names(file_path)
        
        # 尝试多种方法加载第一个工作表
        methods = [
            self._load_excel_standard,
            self._load_excel_data_only,
            self._load_excel_manual
        ]
        
        for method in methods:
            try:
                data = method(file_path, sheet_names[0])
                if data is not None and not data.empty:
                    return data, sheet_names
            except Exception as e:
                continue
        
        raise Exception("无法读取Excel文件，请尝试将文件另存为CSV格式")
    
    def _load_excel_standard(self, file_path, sheet_name):
        """标准Excel加载方法"""
        return self.parent.pd.read_excel(file_path, sheet_name=sheet_name)
    
    def _load_excel_data_only(self, file_path, sheet_name):
        """使用data_only模式加载Excel"""
        return self.parent.pd.read_excel(
            file_path, 
            sheet_name=sheet_name,
            engine='openpyxl',
            engine_kwargs={'data_only': True}
        )
    
    def _load_excel_manual(self, file_path, sheet_name):
        """手动提取Excel数据"""
        openpyxl = self.parent.dep_manager.get_module('openpyxl')
        if not openpyxl:
            raise ImportError("openpyxl not available")
        
        wb = openpyxl.load_workbook(file_path, data_only=True, read_only=True)
        ws = wb[sheet_name] if isinstance(sheet_name, str) else wb.worksheets[sheet_name]
        
        data = []
        for row in ws.iter_rows(values_only=True):
            if any(cell is not None for cell in row):
                data.append(row)
        
        if not data:
            raise ValueError("工作表为空")
        
        columns = [str(col) if col is not None else f'Column_{i}' for i, col in enumerate(data[0])]
        df = self.parent.pd.DataFrame(data[1:], columns=columns)
        return df.dropna(axis=1, how='all')
    
    def get_sheet_names(self, file_path):
        """获取Excel工作表名称"""
        try:
            openpyxl = self.parent.dep_manager.get_module('openpyxl')
            if openpyxl:
                wb = openpyxl.load_workbook(file_path, read_only=True)
                return wb.sheetnames
        except:
            pass
        
        try:
            excel_file = self.parent.pd.ExcelFile(file_path)
            return excel_file.sheet_names
        except:
            return ["Sheet1"]
    
    def process_data(self, data):
        """处理和清理数据"""
        if data is None or data.empty:
            return data
        
        # 清理列名
        data.columns = [str(col).strip() for col in data.columns]
        
        # 删除完全空的行和列
        data = data.dropna(how='all').dropna(axis=1, how='all')
        
        # 智能类型转换
        for col in data.columns:
            data[col] = self._smart_type_conversion(data[col])
        
        return data
    
    def _smart_type_conversion(self, series):
        """智能类型转换"""
        if series.dtype == 'object':
            # 尝试转换为数值
            numeric_series = self.parent.pd.to_numeric(series, errors='coerce')
            valid_ratio = numeric_series.notna().sum() / len(series)
            
            if valid_ratio > 0.7:  # 70%以上可转换为数值
                return numeric_series
            
            # 尝试转换为日期时间
            try:
                datetime_series = self.parent.pd.to_datetime(series, errors='coerce')
                valid_ratio = datetime_series.notna().sum() / len(series)
                if valid_ratio > 0.7:
                    return datetime_series
            except:
                pass
        
        return series
    
    def detect_time_column(self, data):
        """自动检测时间列"""
        if data is None or data.empty:
            return None
        
        time_keywords = ['时间', 'time', '日期', 'date', 'datetime', '时刻', 'timestamp']
        
        # 检查列名
        for col in data.columns:
            col_str = str(col).lower()
            if any(keyword in col_str for keyword in time_keywords):
                return col
        
        # 检查数据类型
        for col in data.columns:
            if 'datetime' in str(data[col].dtype).lower():
                return col
        
        return None


class ChartGenerator:
    """图表生成器 - 处理图表创建和样式设置"""

    def __init__(self, parent):
        self.parent = parent

    def create_chart(self, ax, data, selected_columns, time_col, chart_type, theme):
        """创建图表"""
        if data is None or data.empty or not selected_columns:
            self._create_empty_chart(ax)
            return

        # 准备数据
        plot_data = data.copy()

        # 处理时间列
        x_data = self._prepare_x_data(plot_data, time_col)

        # 应用主题
        self._apply_theme(theme)

        # 绘制图表
        self._plot_data(ax, plot_data, selected_columns, x_data, chart_type)

        # 设置图表样式
        self._set_chart_style(ax, time_col)

        # 优化布局
        self._optimize_layout()

    def _create_empty_chart(self, ax):
        """创建空图表"""
        ax.clear()
        ax.text(0.5, 0.5, '请选择数据文件和列开始绘图',
                horizontalalignment='center', verticalalignment='center',
                transform=ax.transAxes, fontsize=14, color='gray')
        ax.set_xticks([])
        ax.set_yticks([])

    def _prepare_x_data(self, data, time_col):
        """准备X轴数据"""
        if time_col and time_col in data.columns:
            try:
                data[time_col] = self.parent.pd.to_datetime(data[time_col])
                return data[time_col]
            except:
                pass
        return range(len(data))

    def _apply_theme(self, theme):
        """应用主题"""
        theme_config = self.parent.themes.get(theme, self.parent.themes['默认'])
        style = theme_config.get('style', 'default')

        try:
            if style != 'default':
                self.parent.plt.style.use(style)
        except:
            pass  # 如果样式不可用，使用默认样式

    def _plot_data(self, ax, data, selected_columns, x_data, chart_type):
        """绘制数据"""
        ax.clear()

        theme_colors = self.parent.themes[self.parent.theme_var.get()]['colors']

        for i, col in enumerate(selected_columns):
            if col not in data.columns:
                continue

            y_data = data[col].dropna()
            if len(y_data) == 0:
                continue

            # 获取颜色和标签
            color = self.parent.color_vars.get(col, theme_colors[i % len(theme_colors)])
            label = self.parent.label_vars.get(col, col)

            # 根据图表类型绘制
            if chart_type == '线图':
                ax.plot(x_data[:len(y_data)], y_data, color=color, label=label, linewidth=2, marker='o', markersize=4)
            elif chart_type == '散点图':
                ax.scatter(x_data[:len(y_data)], y_data, color=color, label=label, alpha=0.7, s=50)
            elif chart_type == '柱状图':
                ax.bar(x_data[:len(y_data)], y_data, color=color, label=label, alpha=0.8, width=0.8)
            elif chart_type == '面积图':
                ax.fill_between(x_data[:len(y_data)], y_data, color=color, label=label, alpha=0.6)

    def _set_chart_style(self, ax, time_col):
        """设置图表样式"""
        # 设置标题和标签
        ax.set_title(self.parent.title_var.get(), fontsize=14, fontweight='bold', pad=20)
        ax.set_ylabel(self.parent.ylabel_var.get(), fontsize=12)

        # 设置X轴标签
        if time_col:
            ax.set_xlabel('时间', fontsize=12)
            # 格式化时间轴
            try:
                mdates = self.parent.dep_manager.get_module('matplotlib')['mdates']
                ax.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d'))
                ax.xaxis.set_major_locator(mdates.DayLocator(interval=max(1, len(ax.get_xticklabels()) // 10)))
                self.parent.plt.setp(ax.xaxis.get_majorticklabels(), rotation=45, ha='right')
            except:
                pass
        else:
            ax.set_xlabel('数据点', fontsize=12)

        # 设置图例
        if len(ax.get_lines()) > 1 or len(ax.collections) > 1:
            ax.legend(loc='best', frameon=True, fancybox=True, shadow=True)

        # 设置网格
        ax.grid(True, alpha=0.3, linestyle='--')

        # 优化刻度
        ax.tick_params(axis='both', which='major', labelsize=10)

    def _optimize_layout(self):
        """优化布局 - 简化版本，确保稳定显示"""
        try:
            # 使用固定的安全边距，确保所有元素可见
            self.parent.fig.subplots_adjust(
                left=0.12,    # 左边距
                bottom=0.25,  # 底边距 - 确保X轴标签可见
                right=0.95,   # 右边距
                top=0.90,     # 上边距
                wspace=0.2,   # 子图间距
                hspace=0.3
            )
        except Exception as e:
            print(f"布局优化失败: {e}")
            # 使用最基本的布局
            try:
                self.parent.fig.tight_layout(pad=2.0)
            except:
                pass


class OptimizedDataVisualizerGUI:
    """优化版数据可视化GUI - 完整功能，优化结构"""

    def __init__(self, root):
        print("🚀 优化版数据可视化程序启动 v2.0 🚀")
        print("✨ 全面重构：优化布局、增强功能、改进性能")

        self.root = root
        self.root.title("数据可视化程序 - 优化版 v2.0")
        self.root.geometry("1400x900")
        self.root.minsize(800, 600)

        # 初始化依赖管理器
        self.dep_manager = DependencyManager()

        # 检查依赖
        if not self.dep_manager.all_dependencies_available():
            self.show_dependency_error()
            return

        # 获取依赖模块
        self.plt = self.dep_manager.get_module('matplotlib')['plt']
        self.pd = self.dep_manager.get_module('pandas')
        self.np = self.dep_manager.get_module('numpy')

        # 数据相关变量
        self.data = None
        self.file_path = None
        self.sheet_names = []

        # GUI变量
        self.file_path_var = tk.StringVar()
        self.sheet_var = tk.StringVar()
        self.time_col_var = tk.StringVar()
        self.title_var = tk.StringVar(value="数据对比图")
        self.ylabel_var = tk.StringVar(value="数值")
        self.chart_type_var = tk.StringVar(value="线图")
        self.theme_var = tk.StringVar(value="默认")
        self.width_var = tk.StringVar(value="12")
        self.height_var = tk.StringVar(value="8")
        self.status_var = tk.StringVar(value="就绪 - 请选择数据文件")

        # 数据列选择变量 - 优化为字典结构
        self.column_vars = {}
        self.label_vars = {}
        self.color_vars = {}

        # 布局管理
        self.layout_manager = LayoutManager(self)

        # 数据处理器
        self.data_processor = DataProcessor(self)

        # 图表生成器
        self.chart_generator = ChartGenerator(self)

        # 初始化配置
        self.init_configuration()

        # 设置matplotlib
        self.setup_matplotlib()

        # 创建界面
        self.create_widgets()

    def init_configuration(self):
        """初始化配置"""
        # 颜色配置
        self.colors = ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728',
                      '#9467bd', '#8c564b', '#e377c2', '#7f7f7f', '#bcbd22', '#17becf']

        # 主题配置
        self.themes = {
            '默认': {'colors': self.colors, 'style': 'default'},
            '科学': {'colors': ['#0173b2', '#de8f05', '#029e73', '#cc78bc', '#d55e00'], 'style': 'seaborn-v0_8'},
            '深色': {'colors': ['#00d4ff', '#ff6b35', '#4ecdc4', '#ff9999', '#ffd700'], 'style': 'dark_background'},
            '简洁': {'colors': ['#2E86AB', '#A23B72', '#F18F01', '#C73E1D', '#592E83'], 'style': 'seaborn-v0_8-whitegrid'}
        }

        # 图表类型配置
        self.chart_types = ['线图', '散点图', '柱状图', '面积图', '组合图']

        # 布局配置
        self.layout_config = {
            'narrow': {'breakpoint': 900, 'columns_per_row': 2},
            'medium': {'breakpoint': 1200, 'columns_per_row': 3},
            'wide': {'breakpoint': 1500, 'columns_per_row': 4},
            'ultra_wide': {'breakpoint': float('inf'), 'columns_per_row': 6}
        }

    def setup_matplotlib(self):
        """设置matplotlib中文字体和样式"""
        try:
            # 设置中文字体
            self.plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS', 'DejaVu Sans']
            self.plt.rcParams['axes.unicode_minus'] = False

            # 设置默认样式
            self.plt.rcParams['figure.facecolor'] = 'white'
            self.plt.rcParams['axes.facecolor'] = 'white'
            self.plt.rcParams['savefig.facecolor'] = 'white'

            # 设置图表质量
            self.plt.rcParams['figure.dpi'] = 100
            self.plt.rcParams['savefig.dpi'] = 300

            print("✓ matplotlib配置完成")
        except Exception as e:
            print(f"matplotlib配置失败: {e}")

    def create_widgets(self):
        """创建主界面组件"""
        # 创建主框架
        self.main_frame = ttk.Frame(self.root)
        self.main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 创建左右分栏布局
        self.create_left_panel()
        self.create_right_panel()

        # 创建状态栏
        self.create_status_bar()

        # 绑定窗口大小变化事件
        self.root.bind('<Configure>', self.on_window_resize)

        print("✓ 界面创建完成")

    def create_left_panel(self):
        """创建左侧控制面板"""
        # 左侧面板框架
        self.left_frame = ttk.Frame(self.main_frame, width=400)
        self.left_frame.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 10))
        self.left_frame.pack_propagate(False)

        # 1. 文件选择区域
        self.create_file_selection_area()

        # 2. 数据映射区域
        self.create_data_mapping_area()

        # 3. 图表配置区域
        self.create_chart_config_area()

        # 4. 操作按钮区域
        self.create_action_buttons_area()

    def create_file_selection_area(self):
        """创建文件选择区域"""
        file_frame = ttk.LabelFrame(self.left_frame, text="📁 数据源选择", padding=10)
        file_frame.pack(fill=tk.X, pady=(0, 10))

        # 文件路径选择
        ttk.Label(file_frame, text="数据文件:").pack(anchor=tk.W)

        path_frame = ttk.Frame(file_frame)
        path_frame.pack(fill=tk.X, pady=(5, 10))

        self.file_entry = ttk.Entry(path_frame, textvariable=self.file_path_var, state='readonly')
        self.file_entry.pack(side=tk.LEFT, fill=tk.X, expand=True)

        ttk.Button(path_frame, text="浏览", command=self.browse_file).pack(side=tk.RIGHT, padx=(5, 0))

        # 工作表选择
        ttk.Label(file_frame, text="工作表:").pack(anchor=tk.W)
        self.sheet_combo = ttk.Combobox(file_frame, textvariable=self.sheet_var, state='readonly')
        self.sheet_combo.pack(fill=tk.X, pady=(5, 0))
        self.sheet_combo.bind('<<ComboboxSelected>>', self.on_sheet_changed)

    def create_data_mapping_area(self):
        """创建数据映射区域 - 优化为多列布局"""
        mapping_frame = ttk.LabelFrame(self.left_frame, text="📊 数据列映射", padding=10)
        mapping_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))

        # 时间列选择
        ttk.Label(mapping_frame, text="时间列:").pack(anchor=tk.W)
        self.time_combo = ttk.Combobox(mapping_frame, textvariable=self.time_col_var, state='readonly')
        self.time_combo.pack(fill=tk.X, pady=(5, 15))
        self.time_combo.bind('<<ComboboxSelected>>', self.update_preview)

        # 数据列选择标题
        columns_label_frame = ttk.Frame(mapping_frame)
        columns_label_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Label(columns_label_frame, text="选择要绘制的数据列:", font=("Arial", 10, "bold")).pack(side=tk.LEFT)
        ttk.Button(columns_label_frame, text="全选", command=self.select_all_columns).pack(side=tk.RIGHT, padx=(5, 0))
        ttk.Button(columns_label_frame, text="清空", command=self.clear_all_columns).pack(side=tk.RIGHT)

        # 创建滚动区域用于数据列选择 - 多列网格布局
        self.create_columns_scroll_area(mapping_frame)

    def create_columns_scroll_area(self, parent):
        """创建数据列选择的滚动区域 - 响应式多列网格布局"""
        # 滚动框架
        canvas_frame = ttk.Frame(parent)
        canvas_frame.pack(fill=tk.BOTH, expand=True)

        # 创建画布，初始高度根据布局模式调整
        initial_height = self.get_responsive_canvas_height()
        self.columns_canvas = tk.Canvas(canvas_frame, height=initial_height, highlightthickness=0)
        scrollbar = ttk.Scrollbar(canvas_frame, orient="vertical", command=self.columns_canvas.yview)
        self.columns_frame = ttk.Frame(self.columns_canvas)

        self.columns_canvas.configure(yscrollcommand=scrollbar.set)
        self.columns_canvas.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 创建窗口并绑定事件
        self.canvas_window = self.columns_canvas.create_window((0, 0), window=self.columns_frame, anchor="nw")

        # 绑定配置变化事件
        self.columns_frame.bind("<Configure>", self.on_columns_frame_configure)
        self.columns_canvas.bind("<Configure>", self.on_columns_canvas_configure)

        # 绑定鼠标滚轮事件
        self.columns_canvas.bind("<MouseWheel>", self._on_mousewheel)

        # 绑定鼠标进入/离开事件，确保滚轮事件正确处理
        self.columns_canvas.bind("<Enter>", lambda e: self.columns_canvas.focus_set())
        self.columns_canvas.bind("<Leave>", lambda e: self.root.focus_set())

    def get_responsive_canvas_height(self):
        """根据布局模式获取画布高度"""
        layout_mode = self.layout_manager.current_layout_mode

        if layout_mode == 'narrow':
            return 150
        elif layout_mode == 'medium':
            return 200
        else:
            return 250

    def on_columns_frame_configure(self, event):
        """数据列框架配置变化事件"""
        # 更新滚动区域
        self.columns_canvas.configure(scrollregion=self.columns_canvas.bbox("all"))

    def on_columns_canvas_configure(self, event):
        """数据列画布配置变化事件"""
        # 更新内部框架的宽度以匹配画布宽度
        canvas_width = event.width
        self.columns_canvas.itemconfig(self.canvas_window, width=canvas_width)

        # 如果内容高度小于画布高度，调整框架高度
        frame_height = self.columns_frame.winfo_reqheight()
        if frame_height < event.height:
            self.columns_canvas.itemconfig(self.canvas_window, height=event.height)

    def _on_mousewheel(self, event):
        """处理鼠标滚轮事件"""
        self.columns_canvas.yview_scroll(int(-1*(event.delta/120)), "units")

    def create_chart_config_area(self):
        """创建图表配置区域"""
        config_frame = ttk.LabelFrame(self.left_frame, text="🎨 图表配置", padding=10)
        config_frame.pack(fill=tk.X, pady=(0, 10))

        # 图表标题
        ttk.Label(config_frame, text="图表标题:").pack(anchor=tk.W)
        title_entry = ttk.Entry(config_frame, textvariable=self.title_var)
        title_entry.pack(fill=tk.X, pady=(5, 10))
        title_entry.bind('<KeyRelease>', lambda e: self.update_preview())

        # Y轴标签
        ttk.Label(config_frame, text="Y轴标签:").pack(anchor=tk.W)
        ylabel_entry = ttk.Entry(config_frame, textvariable=self.ylabel_var)
        ylabel_entry.pack(fill=tk.X, pady=(5, 10))
        ylabel_entry.bind('<KeyRelease>', lambda e: self.update_preview())

        # 图表类型和主题 - 并排布局
        type_theme_frame = ttk.Frame(config_frame)
        type_theme_frame.pack(fill=tk.X, pady=(0, 10))

        # 图表类型
        type_frame = ttk.Frame(type_theme_frame)
        type_frame.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 5))
        ttk.Label(type_frame, text="图表类型:").pack(anchor=tk.W)
        chart_type_combo = ttk.Combobox(type_frame, textvariable=self.chart_type_var,
                                       values=self.chart_types, state='readonly')
        chart_type_combo.pack(fill=tk.X, pady=(5, 0))
        chart_type_combo.bind('<<ComboboxSelected>>', self.update_preview)

        # 颜色主题
        theme_frame = ttk.Frame(type_theme_frame)
        theme_frame.pack(side=tk.RIGHT, fill=tk.X, expand=True, padx=(5, 0))
        ttk.Label(theme_frame, text="颜色主题:").pack(anchor=tk.W)
        theme_combo = ttk.Combobox(theme_frame, textvariable=self.theme_var,
                                  values=list(self.themes.keys()), state='readonly')
        theme_combo.pack(fill=tk.X, pady=(5, 0))
        theme_combo.bind('<<ComboboxSelected>>', self.update_preview)

        # 图表尺寸
        size_frame = ttk.Frame(config_frame)
        size_frame.pack(fill=tk.X)

        ttk.Label(size_frame, text="图表尺寸:").pack(anchor=tk.W)
        size_input_frame = ttk.Frame(size_frame)
        size_input_frame.pack(fill=tk.X, pady=(5, 0))

        ttk.Label(size_input_frame, text="宽:").pack(side=tk.LEFT)
        width_entry = ttk.Entry(size_input_frame, textvariable=self.width_var, width=8)
        width_entry.pack(side=tk.LEFT, padx=(5, 10))
        width_entry.bind('<KeyRelease>', lambda e: self.update_preview())

        ttk.Label(size_input_frame, text="高:").pack(side=tk.LEFT)
        height_entry = ttk.Entry(size_input_frame, textvariable=self.height_var, width=8)
        height_entry.pack(side=tk.LEFT, padx=(5, 0))
        height_entry.bind('<KeyRelease>', lambda e: self.update_preview())

    def create_action_buttons_area(self):
        """创建操作按钮区域"""
        button_frame = ttk.LabelFrame(self.left_frame, text="🔧 操作", padding=10)
        button_frame.pack(fill=tk.X)

        # 主要操作按钮
        ttk.Button(button_frame, text="🎯 生成图表", command=self.generate_chart).pack(fill=tk.X, pady=(0, 5))
        ttk.Button(button_frame, text="💾 保存图片", command=self.save_chart).pack(fill=tk.X, pady=(0, 5))
        ttk.Button(button_frame, text="🔍 全屏预览", command=self.show_fullscreen).pack(fill=tk.X, pady=(0, 5))

        # 工具按钮
        tools_frame = ttk.Frame(button_frame)
        tools_frame.pack(fill=tk.X, pady=(5, 0))

        ttk.Button(tools_frame, text="📄 转换CSV", command=self.convert_to_csv).pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 2))
        ttk.Button(tools_frame, text="🔄 重置", command=self.reset_all).pack(side=tk.RIGHT, fill=tk.X, expand=True, padx=(2, 0))

    def create_right_panel(self):
        """创建右侧预览面板"""
        # 右侧面板框架
        self.right_frame = ttk.Frame(self.main_frame)
        self.right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)

        # 预览区域
        preview_frame = ttk.LabelFrame(self.right_frame, text="📈 实时预览", padding=5)
        preview_frame.pack(fill=tk.BOTH, expand=True)

        # 创建matplotlib图形
        self.create_matplotlib_canvas(preview_frame)

    def create_matplotlib_canvas(self, parent):
        """创建matplotlib画布"""
        try:
            # 获取matplotlib组件
            FigureCanvasTkAgg = self.dep_manager.get_module('matplotlib')['FigureCanvasTkAgg']
            NavigationToolbar2Tk = self.dep_manager.get_module('matplotlib')['NavigationToolbar2Tk']

            # 创建图形
            self.fig, self.ax = self.plt.subplots(figsize=(10, 6), dpi=100)
            self.fig.patch.set_facecolor('white')

            # 创建画布
            self.canvas = FigureCanvasTkAgg(self.fig, parent)
            canvas_widget = self.canvas.get_tk_widget()
            canvas_widget.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

            # 创建工具栏
            toolbar_frame = ttk.Frame(parent)
            toolbar_frame.pack(side=tk.BOTTOM, fill=tk.X, pady=(5, 0))
            toolbar = NavigationToolbar2Tk(self.canvas, toolbar_frame)
            toolbar.update()

            # 设置初始布局
            self.fig.subplots_adjust(left=0.12, bottom=0.25, right=0.95, top=0.90)

            # 显示初始消息
            self.ax.text(0.5, 0.5, '请选择数据文件开始',
                        horizontalalignment='center', verticalalignment='center',
                        transform=self.ax.transAxes, fontsize=16, color='gray')
            self.ax.set_xticks([])
            self.ax.set_yticks([])

            self.canvas.draw()

            # 绑定画布大小变化事件
            canvas_widget.bind('<Configure>', self.on_canvas_resize)

        except Exception as e:
            print(f"创建matplotlib画布失败: {e}")
            # 创建错误提示标签
            error_label = ttk.Label(parent, text=f"图表预览不可用: {e}",
                                   foreground="red", font=("Arial", 12))
            error_label.pack(expand=True)

    def create_status_bar(self):
        """创建状态栏"""
        status_frame = ttk.Frame(self.main_frame)
        status_frame.pack(side=tk.BOTTOM, fill=tk.X, pady=(10, 0))

        # 状态标签
        ttk.Label(status_frame, text="状态:").pack(side=tk.LEFT)
        status_label = ttk.Label(status_frame, textvariable=self.status_var,
                               foreground="blue", font=("Arial", 9))
        status_label.pack(side=tk.LEFT, padx=(5, 0))

        # 分隔符
        ttk.Separator(status_frame, orient='vertical').pack(side=tk.LEFT, fill=tk.Y, padx=(20, 10))

        # 帮助按钮
        ttk.Button(status_frame, text="❓ 帮助", command=self.show_help).pack(side=tk.RIGHT)

    def on_window_resize(self, event):
        """窗口大小变化事件处理"""
        if event.widget == self.root:
            # 更新布局管理器
            self.layout_manager.update_layout()

            # 如果数据列选择区域存在，更新其高度
            if hasattr(self, 'columns_canvas'):
                new_height = self.get_responsive_canvas_height()
                self.columns_canvas.configure(height=new_height)

    def on_canvas_resize(self, event):
        """画布大小变化事件处理"""
        if hasattr(self, 'fig') and self.fig:
            try:
                # 获取新的画布尺寸
                width_px = event.width
                height_px = event.height

                if width_px > 100 and height_px > 100:
                    # 转换为英寸
                    dpi = self.fig.get_dpi()
                    width_inch = max(3, (width_px - 20) / dpi)
                    height_inch = max(2, (height_px - 20) / dpi)

                    # 更新图表尺寸
                    self.fig.set_size_inches(width_inch, height_inch, forward=True)

                    # 重新绘制
                    self.canvas.draw_idle()

            except Exception as e:
                print(f"画布调整大小失败: {e}")

    def browse_file(self):
        """浏览并选择文件"""
        file_path = filedialog.askopenfilename(
            title="选择数据文件",
            filetypes=[
                ("所有支持格式", "*.csv;*.xlsx;*.xls"),
                ("CSV文件", "*.csv"),
                ("Excel文件", "*.xlsx;*.xls"),
                ("所有文件", "*.*")
            ]
        )

        if file_path:
            self.file_path = file_path
            self.file_path_var.set(os.path.basename(file_path))
            self.load_file()

    def load_file(self):
        """加载文件"""
        try:
            self.status_var.set("正在加载文件...")
            self.root.update()

            # 使用数据处理器加载文件
            self.data, self.sheet_names = self.data_processor.load_file(self.file_path)

            # 更新工作表选择
            self.sheet_combo['values'] = self.sheet_names
            self.sheet_var.set(self.sheet_names[0])

            # 处理数据
            self.data = self.data_processor.process_data(self.data)

            # 更新界面
            self.update_column_selection()

            # 自动检测时间列
            time_col = self.data_processor.detect_time_column(self.data)
            if time_col:
                self.time_col_var.set(time_col)

            # 更新预览
            self.update_preview()

            self.status_var.set(f"文件加载成功！数据形状: {self.data.shape}")
            messagebox.showinfo("成功", f"文件加载成功！\n数据形状: {self.data.shape}")

        except Exception as e:
            print(f"文件加载错误: {e}")
            self.status_var.set("文件加载失败")
            messagebox.showerror("加载失败", f"文件加载失败: {str(e)}")

    def on_sheet_changed(self, event=None):
        """工作表改变时的处理"""
        if self.file_path and self.sheet_var.get():
            try:
                # 重新加载指定工作表
                file_ext = os.path.splitext(self.file_path)[1].lower()
                if file_ext in ['.xlsx', '.xls']:
                    self.data = self.data_processor._load_excel_standard(self.file_path, self.sheet_var.get())
                    self.data = self.data_processor.process_data(self.data)
                    self.update_column_selection()
                    self.update_preview()
            except Exception as e:
                messagebox.showerror("错误", f"工作表切换失败: {str(e)}")

    def update_column_selection(self):
        """更新数据列选择 - 多列网格布局"""
        if self.data is None:
            return

        # 清除现有组件
        for widget in self.columns_frame.winfo_children():
            widget.destroy()

        # 清除变量
        self.column_vars.clear()
        self.label_vars.clear()
        self.color_vars.clear()

        # 获取数值列
        numeric_columns = []
        for col in self.data.columns:
            if self.pd.api.types.is_numeric_dtype(self.data[col]):
                numeric_columns.append(col)

        if not numeric_columns:
            ttk.Label(self.columns_frame, text="未找到数值列", foreground="red").pack()
            return

        # 创建多列网格布局
        self.create_column_grid(numeric_columns)

        # 更新时间列选择
        all_columns = list(self.data.columns)
        self.time_combo['values'] = [''] + all_columns

    def create_column_grid(self, columns):
        """创建数据列的响应式网格布局"""
        columns_per_row = self.layout_manager.columns_per_row

        # 获取响应式配置
        element_config = self.layout_manager.get_responsive_element_config()
        column_width = self.layout_manager.calculate_column_frame_width()

        print(f"🔧 创建响应式网格: {columns_per_row}列/行, 列宽={column_width}px, 模式={element_config['layout_mode']}")

        for i, col in enumerate(columns):
            row = i // columns_per_row
            column = i % columns_per_row

            # 创建列框架 - 设置固定宽度和最小高度
            col_frame = ttk.Frame(self.columns_frame, relief='solid', borderwidth=1)
            col_frame.grid(row=row, column=column, padx=2, pady=2, sticky='nsew')

            # 设置框架的固定宽度
            col_frame.grid_propagate(False)
            col_frame.configure(width=column_width, height=80)

            # 配置列权重 - 确保均匀分布
            self.columns_frame.grid_columnconfigure(column, weight=1, minsize=column_width)
            self.columns_frame.grid_rowconfigure(row, weight=0)

            # 创建内部容器，用于更好的布局控制
            inner_frame = ttk.Frame(col_frame)
            inner_frame.pack(fill=tk.BOTH, expand=True, padx=3, pady=3)

            # 复选框 - 动态文本长度和工具提示
            var = tk.BooleanVar()
            self.column_vars[col] = var

            # 根据配置截断文本
            max_text_length = element_config['checkbox_text_length']
            display_text = col[:max_text_length] + ('...' if len(col) > max_text_length else '')

            checkbox = ttk.Checkbutton(inner_frame, text=display_text,
                                     variable=var, command=self.update_preview)
            checkbox.pack(anchor=tk.W, pady=(0, 2))

            # 添加工具提示显示完整列名
            if len(col) > max_text_length:
                self.create_tooltip(checkbox, col)

            # 标签输入框 - 自适应宽度
            label_var = tk.StringVar(value=col)
            self.label_vars[col] = label_var

            # 根据布局模式调整输入框
            font_size = element_config['entry_font_size']
            if element_config['layout_mode'] == 'compact':
                # 紧凑模式：使用较小的输入框
                label_entry = ttk.Entry(inner_frame, textvariable=label_var,
                                      font=("Arial", font_size), width=8)
            else:
                # 正常模式：自适应宽度
                label_entry = ttk.Entry(inner_frame, textvariable=label_var,
                                      font=("Arial", font_size))

            label_entry.pack(fill=tk.X, pady=(0, 2))
            label_entry.bind('<KeyRelease>', lambda e: self.update_preview())

            # 颜色选择按钮 - 响应式尺寸
            color = self.colors[i % len(self.colors)]
            self.color_vars[col] = color

            button_size = element_config['color_button_size']
            color_button = tk.Button(inner_frame, text="●", fg=color, bg=color,
                                   width=button_size, height=1, relief='raised',
                                   command=lambda c=col: self.choose_color(c))
            color_button.pack(pady=(0, 2))

            # 为颜色按钮添加工具提示
            self.create_tooltip(color_button, f"点击选择 {col} 的颜色")

    def create_tooltip(self, widget, text):
        """创建工具提示"""
        def on_enter(event):
            tooltip = tk.Toplevel()
            tooltip.wm_overrideredirect(True)
            tooltip.wm_geometry(f"+{event.x_root+10}+{event.y_root+10}")

            label = tk.Label(tooltip, text=text, background="lightyellow",
                           relief="solid", borderwidth=1, font=("Arial", 8))
            label.pack()

            widget.tooltip = tooltip

        def on_leave(event):
            if hasattr(widget, 'tooltip'):
                widget.tooltip.destroy()
                del widget.tooltip

        widget.bind("<Enter>", on_enter)
        widget.bind("<Leave>", on_leave)

    def update_column_selection_layout(self):
        """更新数据列选择布局"""
        if hasattr(self, 'data') and self.data is not None:
            # 延迟更新，确保窗口尺寸变化完成
            self.root.after(100, self.update_column_selection)

    def choose_color(self, column):
        """选择颜色"""
        color = colorchooser.askcolor(title=f"选择 {column} 的颜色")[1]
        if color:
            self.color_vars[column] = color
            self.update_preview()

    def select_all_columns(self):
        """全选所有列"""
        for var in self.column_vars.values():
            var.set(True)
        self.update_preview()

    def clear_all_columns(self):
        """清空所有列选择"""
        for var in self.column_vars.values():
            var.set(False)
        self.update_preview()

    def update_preview(self, event=None):
        """更新图表预览"""
        if not hasattr(self, 'ax') or self.data is None:
            return

        try:
            # 获取选中的列
            selected_columns = [col for col, var in self.column_vars.items() if var.get()]

            if not selected_columns:
                self.ax.clear()
                self.ax.text(0.5, 0.5, '请选择要绘制的数据列',
                            horizontalalignment='center', verticalalignment='center',
                            transform=self.ax.transAxes, fontsize=14, color='gray')
                self.ax.set_xticks([])
                self.ax.set_yticks([])
                self.canvas.draw()
                return

            # 使用图表生成器创建图表
            self.chart_generator.create_chart(
                self.ax,
                self.data,
                selected_columns,
                self.time_col_var.get(),
                self.chart_type_var.get(),
                self.theme_var.get()
            )

            self.canvas.draw()

        except Exception as e:
            print(f"预览更新失败: {e}")
            self.ax.clear()
            self.ax.text(0.5, 0.5, f'预览更新失败: {str(e)}',
                        horizontalalignment='center', verticalalignment='center',
                        transform=self.ax.transAxes, fontsize=12, color='red')
            self.canvas.draw()

    def generate_chart(self):
        """生成完整图表"""
        if self.data is None:
            messagebox.showwarning("警告", "请先加载数据文件")
            return

        selected_columns = [col for col, var in self.column_vars.items() if var.get()]
        if not selected_columns:
            messagebox.showwarning("警告", "请选择要绘制的数据列")
            return

        try:
            # 获取图表尺寸
            width = float(self.width_var.get())
            height = float(self.height_var.get())

            # 创建新图表
            fig, ax = self.plt.subplots(figsize=(width, height), dpi=150)

            # 使用图表生成器创建图表
            self.chart_generator.create_chart(
                ax,
                self.data,
                selected_columns,
                self.time_col_var.get(),
                self.chart_type_var.get(),
                self.theme_var.get()
            )

            # 显示图表
            self.plt.show()

            self.status_var.set("图表生成成功")

        except Exception as e:
            print(f"图表生成失败: {e}")
            messagebox.showerror("错误", f"图表生成失败: {str(e)}")

    def save_chart(self):
        """保存图表"""
        if self.data is None:
            messagebox.showwarning("警告", "请先加载数据文件")
            return

        selected_columns = [col for col, var in self.column_vars.items() if var.get()]
        if not selected_columns:
            messagebox.showwarning("警告", "请选择要绘制的数据列")
            return

        # 选择保存路径
        file_path = filedialog.asksaveasfilename(
            title="保存图表",
            defaultextension=".png",
            filetypes=[
                ("PNG图片", "*.png"),
                ("JPG图片", "*.jpg"),
                ("PDF文件", "*.pdf"),
                ("SVG矢量图", "*.svg"),
                ("所有文件", "*.*")
            ]
        )

        if not file_path:
            return

        try:
            # 获取图表尺寸
            width = float(self.width_var.get())
            height = float(self.height_var.get())

            # 创建高质量图表
            fig, ax = self.plt.subplots(figsize=(width, height), dpi=300)

            # 使用图表生成器创建图表
            self.chart_generator.create_chart(
                ax,
                self.data,
                selected_columns,
                self.time_col_var.get(),
                self.chart_type_var.get(),
                self.theme_var.get()
            )

            # 保存图表
            fig.savefig(file_path, dpi=300, bbox_inches='tight',
                       facecolor='white', edgecolor='none')
            self.plt.close(fig)

            self.status_var.set(f"图表已保存: {os.path.basename(file_path)}")
            messagebox.showinfo("成功", f"图表已保存到:\n{file_path}")

        except Exception as e:
            print(f"图表保存失败: {e}")
            messagebox.showerror("错误", f"图表保存失败: {str(e)}")

    def show_fullscreen(self):
        """全屏预览图表"""
        if self.data is None:
            messagebox.showwarning("警告", "请先加载数据文件")
            return

        selected_columns = [col for col, var in self.column_vars.items() if var.get()]
        if not selected_columns:
            messagebox.showwarning("警告", "请选择要绘制的数据列")
            return

        try:
            # 创建全屏窗口
            fullscreen_window = tk.Toplevel(self.root)
            fullscreen_window.title("全屏预览")
            fullscreen_window.state('zoomed')  # Windows下最大化

            # 获取屏幕尺寸
            screen_width = fullscreen_window.winfo_screenwidth()
            screen_height = fullscreen_window.winfo_screenheight()

            # 创建图表
            fig, ax = self.plt.subplots(figsize=(screen_width/100, screen_height/100), dpi=100)

            # 使用图表生成器创建图表
            self.chart_generator.create_chart(
                ax,
                self.data,
                selected_columns,
                self.time_col_var.get(),
                self.chart_type_var.get(),
                self.theme_var.get()
            )

            # 创建画布
            FigureCanvasTkAgg = self.dep_manager.get_module('matplotlib')['FigureCanvasTkAgg']
            canvas = FigureCanvasTkAgg(fig, fullscreen_window)
            canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)

            # 添加关闭按钮
            close_button = ttk.Button(fullscreen_window, text="关闭",
                                    command=lambda: [self.plt.close(fig), fullscreen_window.destroy()])
            close_button.pack(side=tk.BOTTOM, pady=10)

            canvas.draw()

        except Exception as e:
            print(f"全屏预览失败: {e}")
            messagebox.showerror("错误", f"全屏预览失败: {str(e)}")

    def convert_to_csv(self):
        """转换当前文件为CSV格式"""
        if not self.file_path:
            messagebox.showwarning("警告", "请先选择文件")
            return

        file_ext = os.path.splitext(self.file_path)[1].lower()
        if file_ext == '.csv':
            messagebox.showinfo("提示", "当前文件已经是CSV格式")
            return

        if file_ext not in ['.xlsx', '.xls']:
            messagebox.showwarning("警告", "只支持Excel文件转换为CSV")
            return

        # 选择保存路径
        csv_path = filedialog.asksaveasfilename(
            title="保存CSV文件",
            defaultextension=".csv",
            filetypes=[("CSV文件", "*.csv"), ("所有文件", "*.*")]
        )

        if not csv_path:
            return

        try:
            # 转换并保存
            self.data.to_csv(csv_path, index=False, encoding='utf-8-sig')

            self.status_var.set(f"CSV文件已保存: {os.path.basename(csv_path)}")
            messagebox.showinfo("成功", f"CSV文件已保存到:\n{csv_path}")

        except Exception as e:
            print(f"CSV转换失败: {e}")
            messagebox.showerror("错误", f"CSV转换失败: {str(e)}")

    def reset_all(self):
        """重置所有设置"""
        # 重置数据
        self.data = None
        self.file_path = None
        self.sheet_names = []

        # 重置界面变量
        self.file_path_var.set("")
        self.sheet_var.set("")
        self.time_col_var.set("")
        self.title_var.set("数据对比图")
        self.ylabel_var.set("数值")
        self.chart_type_var.set("线图")
        self.theme_var.set("默认")
        self.width_var.set("12")
        self.height_var.set("8")
        self.status_var.set("就绪 - 请选择数据文件")

        # 清除数据列选择
        self.column_vars.clear()
        self.label_vars.clear()
        self.color_vars.clear()

        # 清除界面组件
        if hasattr(self, 'columns_frame'):
            for widget in self.columns_frame.winfo_children():
                widget.destroy()

        # 重置工作表选择
        if hasattr(self, 'sheet_combo'):
            self.sheet_combo['values'] = []

        if hasattr(self, 'time_combo'):
            self.time_combo['values'] = []

        # 重置图表
        if hasattr(self, 'ax'):
            self.ax.clear()
            self.ax.text(0.5, 0.5, '请选择数据文件开始',
                        horizontalalignment='center', verticalalignment='center',
                        transform=self.ax.transAxes, fontsize=16, color='gray')
            self.ax.set_xticks([])
            self.ax.set_yticks([])
            self.canvas.draw()

        messagebox.showinfo("重置", "所有设置已重置")

    def show_dependency_error(self):
        """显示依赖错误并提供解决方案"""
        missing_deps = self.dep_manager.get_missing_dependencies()

        if not missing_deps:
            return

        # 创建错误窗口
        error_window = tk.Toplevel(self.root)
        error_window.title("依赖包缺失")
        error_window.geometry("700x600")
        error_window.transient(self.root)
        error_window.grab_set()

        # 主框架
        main_frame = ttk.Frame(error_window)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # 标题
        title_label = ttk.Label(main_frame, text="数据可视化程序需要以下依赖包：",
                               font=("Arial", 12, "bold"))
        title_label.pack(anchor=tk.W, pady=(0, 15))

        # 依赖状态显示
        deps_frame = ttk.LabelFrame(main_frame, text="依赖包状态", padding=10)
        deps_frame.pack(fill=tk.X, pady=(0, 15))

        for dep_name, dep_info in self.dep_manager.dependencies.items():
            status_frame = ttk.Frame(deps_frame)
            status_frame.pack(fill=tk.X, pady=2)

            if dep_info['available']:
                status_text = f"✓ {dep_name} (已安装)"
                color = "green"
            else:
                status_text = f"✗ {dep_name} (缺失)"
                color = "red"

            ttk.Label(status_frame, text=status_text, foreground=color).pack(side=tk.LEFT)

        # 解决方案
        solutions_frame = ttk.LabelFrame(main_frame, text="解决方案", padding=10)
        solutions_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 15))

        solutions_text = """推荐解决方案（按优先级排序）：

方法1：一键自动安装（推荐）
点击下方"自动安装"按钮，程序将自动安装所有依赖包

方法2：手动安装
在命令提示符中运行：
python -m pip install matplotlib pandas numpy openpyxl

方法3：使用国内镜像（网络慢时）
python -m pip install -i https://pypi.tuna.tsinghua.edu.cn/simple matplotlib pandas numpy openpyxl

方法4：使用Anaconda（推荐新手）
下载并安装Anaconda，自带所有科学计算包
网址：https://www.anaconda.com/

方法5：使用conda安装
conda install matplotlib pandas numpy openpyxl

注意事项：
- 安装完成后请重新启动程序
- 如果遇到权限问题，请以管理员身份运行命令提示符
- 建议使用虚拟环境管理Python包"""

        text_widget = tk.Text(solutions_frame, height=15, wrap=tk.WORD, font=("Consolas", 9))
        scrollbar = ttk.Scrollbar(solutions_frame, orient="vertical", command=text_widget.yview)
        text_widget.configure(yscrollcommand=scrollbar.set)

        text_widget.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        text_widget.insert(tk.END, solutions_text)
        text_widget.config(state=tk.DISABLED)

        # 按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X)

        ttk.Button(button_frame, text="自动安装",
                  command=lambda: self.auto_install_dependencies(error_window)).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="退出",
                  command=self.root.destroy).pack(side=tk.RIGHT)

    def auto_install_dependencies(self, parent_window):
        """自动安装依赖包"""
        # 创建安装进度窗口
        progress_window = tk.Toplevel(parent_window)
        progress_window.title("安装进度")
        progress_window.geometry("500x400")
        progress_window.transient(parent_window)
        progress_window.grab_set()

        # 进度显示
        progress_frame = ttk.Frame(progress_window)
        progress_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        ttk.Label(progress_frame, text="正在安装依赖包...", font=("Arial", 12, "bold")).pack(pady=(0, 10))

        progress_text = tk.Text(progress_frame, height=20, wrap=tk.WORD)
        progress_scrollbar = ttk.Scrollbar(progress_frame, orient="vertical", command=progress_text.yview)
        progress_text.configure(yscrollcommand=progress_scrollbar.set)

        progress_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        progress_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 在新线程中执行安装
        def install_thread():
            try:
                import subprocess
                packages = ['matplotlib', 'pandas', 'numpy', 'openpyxl']

                progress_text.insert(tk.END, "开始安装依赖包...\n")
                progress_text.update()

                for package in packages:
                    progress_text.insert(tk.END, f"\n正在安装 {package}...\n")
                    progress_text.update()

                    # 尝试多种安装方法
                    install_commands = [
                        [sys.executable, "-m", "pip", "install", "--user", package],
                        [sys.executable, "-m", "pip", "install", package],
                        ["pip", "install", "--user", package],
                        ["pip", "install", package]
                    ]

                    success = False
                    for cmd in install_commands:
                        try:
                            result = subprocess.run(cmd, capture_output=True, text=True, timeout=120)
                            if result.returncode == 0:
                                progress_text.insert(tk.END, f"✓ {package} 安装成功\n")
                                success = True
                                break
                            else:
                                progress_text.insert(tk.END, f"命令失败: {' '.join(cmd)}\n")
                        except Exception as e:
                            progress_text.insert(tk.END, f"命令错误: {e}\n")
                        progress_text.update()

                    if not success:
                        progress_text.insert(tk.END, f"✗ {package} 安装失败\n")
                    progress_text.update()

                progress_text.insert(tk.END, "\n安装完成！请重新启动程序。\n")
                messagebox.showinfo("完成", "依赖包安装完成！\n请重新启动程序。")

            except Exception as e:
                progress_text.insert(tk.END, f"\n安装过程出错: {e}\n")
                messagebox.showerror("安装失败", f"自动安装失败: {e}")

            finally:
                progress_window.destroy()
                parent_window.destroy()
                self.root.destroy()

        thread = threading.Thread(target=install_thread, daemon=True)
        thread.start()

    def show_help(self):
        """显示帮助信息"""
        help_window = tk.Toplevel(self.root)
        help_window.title("使用帮助")
        help_window.geometry("600x500")
        help_window.transient(self.root)

        help_text = """数据可视化程序使用帮助 v2.0

📁 数据源选择：
• 支持CSV和Excel文件格式
• Excel文件支持多工作表选择
• 自动检测文件编码和数据类型

📊 数据列映射：
• 自动识别数值列用于绘图
• 支持多列选择，网格布局显示
• 可自定义列标签和颜色
• 支持时间列自动检测

🎨 图表配置：
• 多种图表类型：线图、散点图、柱状图、面积图
• 多种颜色主题：默认、科学、深色、简洁
• 可调整图表标题、Y轴标签和尺寸

🔧 操作功能：
• 实时预览：选择数据后自动更新预览
• 生成图表：创建独立的高质量图表窗口
• 保存图片：支持PNG、JPG、PDF、SVG格式
• 全屏预览：全屏显示图表
• CSV转换：将Excel文件转换为CSV格式

💡 使用技巧：
• 程序支持响应式布局，会根据窗口大小调整界面
• 数据列选择区域支持滚动，可处理大量数据列
• 图表预览会自动优化布局，确保所有元素可见
• 支持鼠标滚轮在数据列选择区域滚动

❓ 常见问题：
• 如果图表显示不完整，尝试调整窗口大小
• Excel文件读取失败时，建议转换为CSV格式
• 中文显示异常时，检查系统字体设置

版本：2.0 - 优化重构版
"""

        text_widget = tk.Text(help_window, wrap=tk.WORD, font=("Arial", 10))
        scrollbar = ttk.Scrollbar(help_window, orient="vertical", command=text_widget.yview)
        text_widget.configure(yscrollcommand=scrollbar.set)

        text_widget.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=10, pady=10)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y, pady=10)

        text_widget.insert(tk.END, help_text)
        text_widget.config(state=tk.DISABLED)


# 主程序入口
def main():
    """主程序入口"""
    try:
        # 创建主窗口
        root = tk.Tk()

        # 设置窗口图标（如果有的话）
        try:
            # root.iconbitmap('icon.ico')  # 如果有图标文件
            pass
        except:
            pass

        # 创建应用程序
        app = OptimizedDataVisualizerGUI(root)

        # 启动主循环
        root.mainloop()

    except Exception as e:
        print(f"程序启动失败: {e}")
        import traceback
        traceback.print_exc()

        # 显示错误对话框
        try:
            messagebox.showerror("启动失败", f"程序启动失败:\n{str(e)}")
        except:
            print("无法显示错误对话框")


if __name__ == "__main__":
    main()
