#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生成测试11 vs 测试5的折线图对比
简化版本，专门用于生成折线图
"""

def main():
    try:
        print("正在导入必要的库...")
        import pandas as pd
        import matplotlib.pyplot as plt
        import matplotlib
        
        # 设置matplotlib
        matplotlib.use('Agg')  # 使用非交互式后端
        plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
        plt.rcParams['axes.unicode_minus'] = False
        
        print("正在读取数据...")
        # 读取数据
        df11 = pd.read_csv('11_fixed_headers.csv')
        df5 = pd.read_csv('5_fixed_headers.csv')
        
        print(f"测试11数据: {len(df11)} 个数据点")
        print(f"测试5数据: {len(df5)} 个数据点")
        
        # 颜色设置
        colors = {
            '11': '#FF6B6B',  # 红色 - 测试11
            '5': '#4ECDC4'    # 青色 - 测试5
        }
        
        print("正在生成关键参数对比图...")
        # 1. 生成关键参数对比图 (2x2)
        create_key_parameters_chart(df11, df5, colors, plt)
        
        print("正在生成制冷效果对比图...")
        # 2. 生成制冷效果对比图
        create_cooling_effect_chart(df11, df5, colors, plt)
        
        print("正在生成完整参数对比图...")
        # 3. 生成完整参数对比图 (3x3)
        create_complete_chart(df11, df5, colors, plt)
        
        print("\n" + "="*50)
        print("✅ 所有折线图生成完成！")
        print("\n生成的图表文件：")
        print("📊 key_parameters_comparison.png - 关键参数对比")
        print("❄️ cooling_effect_comparison.png - 制冷效果对比")
        print("📈 complete_parameters_comparison.png - 完整参数对比")
        print("="*50)
        
    except ImportError as e:
        print(f"❌ 缺少必要的库: {e}")
        print("请安装: pip install pandas matplotlib")
    except FileNotFoundError as e:
        print(f"❌ 找不到数据文件: {e}")
        print("请确保 11_fixed_headers.csv 和 5_fixed_headers.csv 文件存在")
    except Exception as e:
        print(f"❌ 发生错误: {e}")
        import traceback
        traceback.print_exc()

def create_key_parameters_chart(df11, df5, colors, plt):
    """创建关键参数对比图"""
    key_params = ['环境温度', '遮阳罩表面温度', '制冷帐篷表面温度', '皮革表面温度']
    
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    fig.suptitle('测试11 vs 测试5 - 关键温度参数折线图对比', fontsize=16, fontweight='bold')
    
    for i, param in enumerate(key_params):
        row = i // 2
        col = i % 2
        ax = axes[row, col]
        
        # 绘制折线
        ax.plot(df11['时间']/60, df11[param], 
               color=colors['11'], linewidth=3, alpha=0.9, 
               label='测试11', marker='o', markersize=1.5)
        ax.plot(df5['时间']/60, df5[param], 
               color=colors['5'], linewidth=3, alpha=0.9, 
               label='测试5', marker='s', markersize=1.5)
        
        ax.set_title(param, fontsize=14, fontweight='bold', pad=15)
        ax.set_xlabel('时间 (分钟)', fontsize=12)
        ax.set_ylabel('温度 (°C)', fontsize=12)
        ax.legend(fontsize=11, loc='best')
        ax.grid(True, alpha=0.4, linestyle='-', linewidth=0.5)
        
        # 添加统计信息
        start_11 = df11[param].iloc[0]
        end_11 = df11[param].iloc[-1]
        start_5 = df5[param].iloc[0]
        end_5 = df5[param].iloc[-1]
        
        change_11 = end_11 - start_11
        change_5 = end_5 - start_5
        
        stats_text = f'测试11: {start_11:.1f}°C → {end_11:.1f}°C ({change_11:+.1f}°C)\n'
        stats_text += f'测试5: {start_5:.1f}°C → {end_5:.1f}°C ({change_5:+.1f}°C)\n'
        stats_text += f'最终差值: {end_11-end_5:+.1f}°C'
        
        ax.text(0.02, 0.98, stats_text, transform=ax.transAxes, fontsize=10,
               bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8),
               verticalalignment='top')
    
    plt.tight_layout()
    plt.subplots_adjust(top=0.93)
    plt.savefig('key_parameters_comparison.png', dpi=300, bbox_inches='tight')
    plt.close()

def create_cooling_effect_chart(df11, df5, colors, plt):
    """创建制冷效果专项对比图"""
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(18, 8))
    fig.suptitle('制冷效果专项对比分析 - 测试11 vs 测试5', fontsize=16, fontweight='bold')
    
    # 左图：制冷帐篷温度对比
    ax1.plot(df11['时间']/60, df11['制冷帐篷表面温度'], 
            color=colors['11'], linewidth=3, label='测试11-表面', alpha=0.9)
    ax1.plot(df11['时间']/60, df11['制冷帐篷背面温度'], 
            color=colors['11'], linewidth=2, linestyle='--', label='测试11-背面', alpha=0.7)
    ax1.plot(df5['时间']/60, df5['制冷帐篷表面温度'], 
            color=colors['5'], linewidth=3, label='测试5-表面', alpha=0.9)
    ax1.plot(df5['时间']/60, df5['制冷帐篷背面温度'], 
            color=colors['5'], linewidth=2, linestyle='--', label='测试5-背面', alpha=0.7)
    
    ax1.set_title('制冷帐篷温度对比', fontsize=14, fontweight='bold')
    ax1.set_xlabel('时间 (分钟)', fontsize=12)
    ax1.set_ylabel('温度 (°C)', fontsize=12)
    ax1.legend(fontsize=10)
    ax1.grid(True, alpha=0.3)
    
    # 右图：皮革温度对比
    ax2.plot(df11['时间']/60, df11['皮革表面温度'], 
            color=colors['11'], linewidth=3, label='测试11-表面', alpha=0.9)
    ax2.plot(df11['时间']/60, df11['皮革背面温度'], 
            color=colors['11'], linewidth=2, linestyle='--', label='测试11-背面', alpha=0.7)
    ax2.plot(df5['时间']/60, df5['皮革表面温度'], 
            color=colors['5'], linewidth=3, label='测试5-表面', alpha=0.9)
    ax2.plot(df5['时间']/60, df5['皮革背面温度'], 
            color=colors['5'], linewidth=2, linestyle='--', label='测试5-背面', alpha=0.7)
    
    ax2.set_title('皮革温度对比', fontsize=14, fontweight='bold')
    ax2.set_xlabel('时间 (分钟)', fontsize=12)
    ax2.set_ylabel('温度 (°C)', fontsize=12)
    ax2.legend(fontsize=10)
    ax2.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.subplots_adjust(top=0.9)
    plt.savefig('cooling_effect_comparison.png', dpi=300, bbox_inches='tight')
    plt.close()

def create_complete_chart(df11, df5, colors, plt):
    """创建完整参数对比图"""
    temp_columns = [
        '遮阳罩表面温度', '遮阳罩背面温度', '遮阳罩皮革表面温度',
        '制冷帐篷表面温度', '制冷帐篷背面温度', '制冷帐篷皮革温度',
        '皮革表面温度', '皮革背面温度', '环境温度'
    ]
    
    fig, axes = plt.subplots(3, 3, figsize=(20, 16))
    fig.suptitle('测试11 vs 测试5 - 完整温度参数折线图对比', fontsize=18, fontweight='bold')
    
    for i, col in enumerate(temp_columns):
        row = i // 3
        col_idx = i % 3
        ax = axes[row, col_idx]
        
        # 绘制折线
        ax.plot(df11['时间']/60, df11[col], 
               color=colors['11'], linewidth=2.5, alpha=0.8, 
               label='测试11', marker='o', markersize=0.8)
        ax.plot(df5['时间']/60, df5[col], 
               color=colors['5'], linewidth=2.5, alpha=0.8, 
               label='测试5', marker='s', markersize=0.8)
        
        ax.set_title(col, fontsize=12, fontweight='bold', pad=10)
        ax.set_xlabel('时间 (分钟)', fontsize=10)
        ax.set_ylabel('温度 (°C)', fontsize=10)
        ax.legend(fontsize=9, loc='best')
        ax.grid(True, alpha=0.3, linestyle='--')
        
        # 添加统计信息
        avg_11 = df11[col].mean()
        avg_5 = df5[col].mean()
        diff = avg_11 - avg_5
        
        stats_text = f'平均差: {diff:+.1f}°C\n'
        stats_text += f'测试11: {avg_11:.1f}°C\n'
        stats_text += f'测试5: {avg_5:.1f}°C'
        
        ax.text(0.02, 0.98, stats_text, transform=ax.transAxes, fontsize=8,
               bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8),
               verticalalignment='top')
    
    plt.tight_layout()
    plt.subplots_adjust(top=0.93)
    plt.savefig('complete_parameters_comparison.png', dpi=300, bbox_inches='tight')
    plt.close()

if __name__ == "__main__":
    main()
