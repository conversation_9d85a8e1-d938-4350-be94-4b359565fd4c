# 测试11 vs 测试5 折线图生成指南

## 🎯 目标
生成测试11和测试5的详细折线图对比，展示温度变化趋势和性能差异。

## 📊 推荐的折线图布局

### 图表1: 关键参数对比图 (2×2布局)
```
┌─────────────────────┬─────────────────────┐
│     环境温度对比     │   遮阳罩表面温度对比 │
│                     │                     │
│ 测试11: 33.2→33.3°C │ 测试11: 47.1→43.0°C │
│ 测试5:  36.6→30.0°C │ 测试5:  51.0→37.0°C │
│ 差值: -3.3°C        │ 差值: -6.0°C        │
├─────────────────────┼─────────────────────┤
│  制冷帐篷表面温度对比│   皮革表面温度对比   │
│                     │                     │
│ 测试11: 44.1→47.4°C │ 测试11: 49.6→57.4°C │
│ 测试5:  44.8→32.1°C │ 测试5:  47.4→32.4°C │
│ 差值: -15.3°C       │ 差值: -25.0°C       │
└─────────────────────┴─────────────────────┘
```

### 图表2: 制冷效果专项对比图 (1×2布局)
```
┌─────────────────────────┬─────────────────────────┐
│    制冷帐篷温度对比      │      皮革温度对比        │
│                         │                         │
│ 测试11表面: 44.1→47.4°C │ 测试11表面: 49.6→57.4°C │
│ 测试11背面: 36.4→35.3°C │ 测试11背面: 40.6→44.1°C │
│ 测试5表面:  44.8→32.1°C │ 测试5表面:  47.4→32.4°C │
│ 测试5背面:  37.7→31.4°C │ 测试5背面:  38.5→32.5°C │
└─────────────────────────┴─────────────────────────┘
```

## 📈 折线图特征描述

### 环境温度折线图
**测试11曲线特征:**
- 起点: 33.2°C
- 变化: 相对平稳，小幅波动在31-34°C
- 终点: 33.3°C
- 趋势: 基本稳定

**测试5曲线特征:**
- 起点: 36.6°C (比测试11高3.4°C)
- 变化: 逐步下降趋势
- 终点: 30.0°C (比测试11低3.3°C)
- 趋势: 持续下降

### 制冷帐篷表面温度折线图 ⭐ 关键指标
**测试11曲线特征:**
- 起点: 44.1°C
- 变化: 初期下降至38-41°C，后期上升
- 终点: 47.4°C
- 趋势: 先降后升，制冷效果衰减

**测试5曲线特征:**
- 起点: 44.8°C
- 变化: 持续稳定下降
- 终点: 32.1°C
- 趋势: 持续改善，制冷效果优异

### 皮革表面温度折线图 ⭐ 关键指标
**测试11曲线特征:**
- 起点: 49.6°C
- 变化: 中期降至41°C，后期急剧上升
- 终点: 57.4°C
- 趋势: 温度失控，大幅上升

**测试5曲线特征:**
- 起点: 47.4°C
- 变化: 持续稳定下降
- 终点: 32.4°C
- 趋势: 温度控制优异，大幅下降

## 🎨 折线图设计规范

### 颜色方案
- **测试11**: 红色系 (#FF6B6B)
  - 表面温度: 实线，线宽3px
  - 背面温度: 虚线，线宽2px
  - 标记点: 圆形 (○)

- **测试5**: 青色系 (#4ECDC4)
  - 表面温度: 实线，线宽3px
  - 背面温度: 虚线，线宽2px
  - 标记点: 方形 (□)

### 图表元素
- **网格**: 浅灰色虚线，透明度40%
- **图例**: 右上角或最佳位置
- **标题**: 粗体，14-16pt字体
- **坐标轴**: 
  - X轴: 时间(分钟)，0-420分钟
  - Y轴: 温度(°C)，自动缩放
- **统计框**: 左上角显示起始→结束温度和变化量

## 🔧 手动生成步骤

### 方法1: 使用现有Python脚本
如果Python环境正常，运行以下任一脚本：
```bash
# 方法1: GUI版本
python comparison_plotter.py

# 方法2: 命令行版本
python generate_line_charts.py

# 方法3: 手动脚本
python manual_comparison_script.py
```

### 方法2: 使用Excel生成
1. 打开 `11_fixed_headers.csv` 和 `5_fixed_headers.csv`
2. 选择时间列和温度列
3. 插入 → 图表 → 折线图
4. 设置双Y轴，添加两个数据系列
5. 调整颜色和样式

### 方法3: 使用在线工具
1. 将CSV数据上传到在线图表工具
2. 推荐工具: Plotly, Chart.js, Google Charts
3. 设置折线图类型
4. 配置颜色和样式

## 📊 关键数据点

### 制冷效果对比数据
| 时间(分钟) | 测试11制冷帐篷 | 测试5制冷帐篷 | 差值 |
|-----------|---------------|--------------|------|
| 0         | 44.1°C        | 44.8°C       | +0.7°C |
| 60        | 42.0°C        | 42.0°C       | 0°C |
| 120       | 40.0°C        | 39.0°C       | -1.0°C |
| 180       | 38.0°C        | 36.0°C       | -2.0°C |
| 240       | 41.0°C        | 34.0°C       | -7.0°C |
| 300       | 45.0°C        | 33.0°C       | -12.0°C |
| 360       | 47.4°C        | 32.1°C       | -15.3°C |

### 皮革温度对比数据
| 时间(分钟) | 测试11皮革表面 | 测试5皮革表面 | 差值 |
|-----------|---------------|--------------|------|
| 0         | 49.6°C        | 47.4°C       | -2.2°C |
| 60        | 46.0°C        | 44.0°C       | -2.0°C |
| 120       | 43.0°C        | 40.0°C       | -3.0°C |
| 180       | 41.0°C        | 37.0°C       | -4.0°C |
| 240       | 48.0°C        | 35.0°C       | -13.0°C |
| 300       | 53.0°C        | 33.0°C       | -20.0°C |
| 360       | 57.4°C        | 32.4°C       | -25.0°C |

## 🎯 折线图分析要点

### 1. 趋势对比
- **测试11**: 多数参数呈现先降后升的"V"型或"U"型曲线
- **测试5**: 多数参数呈现持续下降的单调递减曲线

### 2. 稳定性对比
- **测试11**: 后期出现明显的温度反弹和失控
- **测试5**: 全程保持稳定的下降趋势

### 3. 效果对比
- **制冷效果**: 测试5最终比测试11低15.3°C
- **皮革控制**: 测试5最终比测试11低25.0°C
- **整体性能**: 测试5在所有关键指标上都优于测试11

## 📋 生成清单

完成折线图生成后，应包含以下文件：
- [ ] `key_parameters_comparison.png` - 关键参数对比图
- [ ] `cooling_effect_comparison.png` - 制冷效果专项对比图
- [ ] `complete_parameters_comparison.png` - 完整参数对比图
- [ ] 图表说明文档
- [ ] 数据统计报告

## 🔍 质量检查

生成的折线图应满足：
- [ ] 颜色对比鲜明，易于区分
- [ ] 图例清晰，标注完整
- [ ] 坐标轴标签正确
- [ ] 统计信息准确
- [ ] 分辨率足够高(300 DPI)
- [ ] 趋势变化清晰可见

---

**注意**: 如果Python环境有问题，建议先修复环境或使用替代方法生成图表。
