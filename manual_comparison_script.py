#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
手动运行的测试11 vs 测试5对比脚本
如果Python环境正常，可以直接运行此脚本生成折线图对比
"""

def main():
    """主函数 - 生成对比图表"""
    print("测试11 vs 测试5 折线图对比分析")
    print("=" * 50)
    
    try:
        # 检查并导入依赖
        print("正在检查依赖包...")
        import pandas as pd
        import matplotlib.pyplot as plt
        import numpy as np
        print("✓ 所有依赖包可用")
        
        # 设置中文字体
        plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
        plt.rcParams['axes.unicode_minus'] = False
        
        # 读取数据
        print("正在读取数据文件...")
        df11 = pd.read_csv('11_fixed_headers.csv')
        df5 = pd.read_csv('5_fixed_headers.csv')
        print(f"✓ 测试11数据: {len(df11)} 个数据点")
        print(f"✓ 测试5数据: {len(df5)} 个数据点")
        
        # 温度参数列表
        temp_columns = [
            '遮阳罩表面温度', '遮阳罩背面温度', '遮阳罩皮革表面温度',
            '制冷帐篷表面温度', '制冷帐篷背面温度', '制冷帐篷皮革温度',
            '皮革表面温度', '皮革背面温度', '环境温度'
        ]
        
        # 颜色设置
        colors = {
            '11': '#FF6B6B',  # 红色系 - 测试11
            '5': '#4ECDC4'    # 青色系 - 测试5
        }
        
        print("\n正在生成图表...")
        
        # 1. 生成完整对比图 (3x3)
        print("1. 生成完整9参数对比图...")
        create_complete_comparison(df11, df5, temp_columns, colors, plt)
        
        # 2. 生成关键参数对比图 (2x2)
        print("2. 生成关键参数对比图...")
        create_key_comparison(df11, df5, colors, plt)
        
        # 3. 生成制冷效果专项对比图
        print("3. 生成制冷效果专项对比图...")
        create_cooling_comparison(df11, df5, colors, plt)
        
        # 4. 生成数据统计报告
        print("4. 生成数据统计报告...")
        create_statistics_report(df11, df5, temp_columns)
        
        print("\n" + "=" * 50)
        print("✅ 所有对比图表生成完成！")
        print("\n生成的文件：")
        print("📊 complete_comparison_manual.png - 完整9参数对比")
        print("📈 key_comparison_manual.png - 关键参数对比")
        print("❄️ cooling_comparison_manual.png - 制冷效果专项对比")
        print("📋 comparison_statistics.txt - 详细统计报告")
        print("=" * 50)
        
    except ImportError as e:
        print(f"❌ 依赖包缺失: {e}")
        print("\n请安装必要的包：")
        print("pip install pandas matplotlib numpy")
        
    except FileNotFoundError as e:
        print(f"❌ 文件未找到: {e}")
        print("\n请确保以下文件存在于当前目录：")
        print("- 11_fixed_headers.csv")
        print("- 5_fixed_headers.csv")
        
    except Exception as e:
        print(f"❌ 错误: {e}")
        import traceback
        traceback.print_exc()

def create_complete_comparison(df11, df5, temp_columns, colors, plt):
    """创建完整的9参数对比图"""
    fig, axes = plt.subplots(3, 3, figsize=(20, 16))
    fig.suptitle('测试11 vs 测试5 - 完整温度对比折线图', fontsize=18, fontweight='bold')
    
    for i, col in enumerate(temp_columns):
        row = i // 3
        col_idx = i % 3
        ax = axes[row, col_idx]
        
        # 绘制折线
        ax.plot(df11['时间']/60, df11[col], 
               color=colors['11'], linewidth=2.5, alpha=0.8, 
               label='测试11', marker='o', markersize=0.8)
        ax.plot(df5['时间']/60, df5[col], 
               color=colors['5'], linewidth=2.5, alpha=0.8, 
               label='测试5', marker='s', markersize=0.8)
        
        ax.set_title(col, fontsize=12, fontweight='bold', pad=10)
        ax.set_xlabel('时间 (分钟)', fontsize=10)
        ax.set_ylabel('温度 (°C)', fontsize=10)
        ax.legend(fontsize=9, loc='best')
        ax.grid(True, alpha=0.3, linestyle='--')
        
        # 添加统计信息
        avg_11 = df11[col].mean()
        avg_5 = df5[col].mean()
        diff = avg_11 - avg_5
        
        stats_text = f'平均差: {diff:+.1f}°C\n'
        stats_text += f'测试11: {avg_11:.1f}°C\n'
        stats_text += f'测试5: {avg_5:.1f}°C'
        
        ax.text(0.02, 0.98, stats_text, transform=ax.transAxes, fontsize=8,
               bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8),
               verticalalignment='top')
    
    plt.tight_layout()
    plt.subplots_adjust(top=0.93)
    plt.savefig('complete_comparison_manual.png', dpi=300, bbox_inches='tight')
    plt.close()

def create_key_comparison(df11, df5, colors, plt):
    """创建关键参数对比图"""
    key_params = ['环境温度', '遮阳罩表面温度', '制冷帐篷表面温度', '皮革表面温度']
    
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    fig.suptitle('关键温度参数对比 - 测试11 vs 测试5', fontsize=16, fontweight='bold')
    
    for i, param in enumerate(key_params):
        row = i // 2
        col = i % 2
        ax = axes[row, col]
        
        ax.plot(df11['时间']/60, df11[param], 
               color=colors['11'], linewidth=3, alpha=0.9, 
               label='测试11', marker='o', markersize=1.5)
        ax.plot(df5['时间']/60, df5[param], 
               color=colors['5'], linewidth=3, alpha=0.9, 
               label='测试5', marker='s', markersize=1.5)
        
        ax.set_title(param, fontsize=14, fontweight='bold', pad=15)
        ax.set_xlabel('时间 (分钟)', fontsize=12)
        ax.set_ylabel('温度 (°C)', fontsize=12)
        ax.legend(fontsize=11, loc='best')
        ax.grid(True, alpha=0.4, linestyle='-', linewidth=0.5)
        
        # 添加详细统计信息
        start_11 = df11[param].iloc[0]
        end_11 = df11[param].iloc[-1]
        start_5 = df5[param].iloc[0]
        end_5 = df5[param].iloc[-1]
        
        change_11 = end_11 - start_11
        change_5 = end_5 - start_5
        
        stats_text = f'测试11: {start_11:.1f}°C → {end_11:.1f}°C ({change_11:+.1f}°C)\n'
        stats_text += f'测试5: {start_5:.1f}°C → {end_5:.1f}°C ({change_5:+.1f}°C)\n'
        stats_text += f'最终差值: {end_11-end_5:+.1f}°C'
        
        ax.text(0.02, 0.98, stats_text, transform=ax.transAxes, fontsize=10,
               bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8),
               verticalalignment='top')
    
    plt.tight_layout()
    plt.subplots_adjust(top=0.93)
    plt.savefig('key_comparison_manual.png', dpi=300, bbox_inches='tight')
    plt.close()

def create_cooling_comparison(df11, df5, colors, plt):
    """创建制冷效果专项对比图"""
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(18, 8))
    fig.suptitle('制冷效果专项对比分析 - 测试11 vs 测试5', fontsize=16, fontweight='bold')
    
    # 左图：制冷帐篷温度对比
    ax1.plot(df11['时间']/60, df11['制冷帐篷表面温度'], 
            color=colors['11'], linewidth=3, label='测试11-表面', alpha=0.9)
    ax1.plot(df11['时间']/60, df11['制冷帐篷背面温度'], 
            color=colors['11'], linewidth=2, linestyle='--', label='测试11-背面', alpha=0.7)
    ax1.plot(df5['时间']/60, df5['制冷帐篷表面温度'], 
            color=colors['5'], linewidth=3, label='测试5-表面', alpha=0.9)
    ax1.plot(df5['时间']/60, df5['制冷帐篷背面温度'], 
            color=colors['5'], linewidth=2, linestyle='--', label='测试5-背面', alpha=0.7)
    
    ax1.set_title('制冷帐篷温度对比', fontsize=14, fontweight='bold')
    ax1.set_xlabel('时间 (分钟)', fontsize=12)
    ax1.set_ylabel('温度 (°C)', fontsize=12)
    ax1.legend(fontsize=10)
    ax1.grid(True, alpha=0.3)
    
    # 右图：皮革温度对比
    ax2.plot(df11['时间']/60, df11['皮革表面温度'], 
            color=colors['11'], linewidth=3, label='测试11-表面', alpha=0.9)
    ax2.plot(df11['时间']/60, df11['皮革背面温度'], 
            color=colors['11'], linewidth=2, linestyle='--', label='测试11-背面', alpha=0.7)
    ax2.plot(df5['时间']/60, df5['皮革表面温度'], 
            color=colors['5'], linewidth=3, label='测试5-表面', alpha=0.9)
    ax2.plot(df5['时间']/60, df5['皮革背面温度'], 
            color=colors['5'], linewidth=2, linestyle='--', label='测试5-背面', alpha=0.7)
    
    ax2.set_title('皮革温度对比', fontsize=14, fontweight='bold')
    ax2.set_xlabel('时间 (分钟)', fontsize=12)
    ax2.set_ylabel('温度 (°C)', fontsize=12)
    ax2.legend(fontsize=10)
    ax2.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.subplots_adjust(top=0.9)
    plt.savefig('cooling_comparison_manual.png', dpi=300, bbox_inches='tight')
    plt.close()

def create_statistics_report(df11, df5, temp_columns):
    """生成详细统计报告"""
    report = []
    report.append("=" * 80)
    report.append("测试11 vs 测试5 - 详细统计对比报告")
    report.append("=" * 80)
    report.append("")
    
    # 基本信息
    report.append("📊 基本信息对比:")
    report.append(f"   测试11数据点: {len(df11):,} 个")
    report.append(f"   测试5数据点:  {len(df5):,} 个")
    report.append(f"   数据点差值:   {len(df5) - len(df11):,} 个 ({((len(df5) - len(df11))/len(df11)*100):+.1f}%)")
    report.append("")
    report.append(f"   测试11持续时间: {df11['时间'].max():.1f} 秒 ({df11['时间'].max()/60:.1f} 分钟)")
    report.append(f"   测试5持续时间:  {df5['时间'].max():.1f} 秒 ({df5['时间'].max()/60:.1f} 分钟)")
    report.append(f"   时间差值:       {(df5['时间'].max() - df11['时间'].max())/60:.1f} 分钟")
    report.append("")
    
    # 详细温度对比
    report.append("🌡️ 详细温度参数对比:")
    report.append("")
    report.append(f"{'参数名称':<15} {'测试11平均':<10} {'测试5平均':<10} {'平均差值':<10} {'测试11最高':<10} {'测试5最高':<10} {'最高差值':<10}")
    report.append("-" * 90)
    
    for col in temp_columns:
        avg_11 = df11[col].mean()
        avg_5 = df5[col].mean()
        avg_diff = avg_11 - avg_5
        
        max_11 = df11[col].max()
        max_5 = df5[col].max()
        max_diff = max_11 - max_5
        
        report.append(f"{col:<15} {avg_11:>9.2f} {avg_5:>9.2f} {avg_diff:>+9.2f} {max_11:>9.2f} {max_5:>9.2f} {max_diff:>+9.2f}")
    
    report.append("")
    
    # 关键发现
    report.append("🎯 关键发现:")
    
    # 制冷效果分析
    cooling_start_11 = df11['制冷帐篷表面温度'].iloc[0]
    cooling_end_11 = df11['制冷帐篷表面温度'].iloc[-1]
    cooling_start_5 = df5['制冷帐篷表面温度'].iloc[0]
    cooling_end_5 = df5['制冷帐篷表面温度'].iloc[-1]
    
    report.append(f"   制冷效果对比:")
    report.append(f"     测试11: {cooling_start_11:.1f}°C → {cooling_end_11:.1f}°C (变化: {cooling_end_11-cooling_start_11:+.1f}°C)")
    report.append(f"     测试5:  {cooling_start_5:.1f}°C → {cooling_end_5:.1f}°C (变化: {cooling_end_5-cooling_start_5:+.1f}°C)")
    report.append(f"     结论: 测试5制冷效果更好，最终温度低 {cooling_end_11-cooling_end_5:.1f}°C")
    report.append("")
    
    # 皮革温度分析
    leather_start_11 = df11['皮革表面温度'].iloc[0]
    leather_end_11 = df11['皮革表面温度'].iloc[-1]
    leather_start_5 = df5['皮革表面温度'].iloc[0]
    leather_end_5 = df5['皮革表面温度'].iloc[-1]
    
    report.append(f"   皮革温度控制对比:")
    report.append(f"     测试11: {leather_start_11:.1f}°C → {leather_end_11:.1f}°C (变化: {leather_end_11-leather_start_11:+.1f}°C)")
    report.append(f"     测试5:  {leather_start_5:.1f}°C → {leather_end_5:.1f}°C (变化: {leather_end_5-leather_start_5:+.1f}°C)")
    report.append(f"     结论: 测试5皮革温度控制优异，最终温度低 {leather_end_11-leather_end_5:.1f}°C")
    report.append("")
    
    report.append("=" * 80)
    report.append("报告生成完成")
    report.append("=" * 80)
    
    # 保存报告
    with open('comparison_statistics.txt', 'w', encoding='utf-8') as f:
        f.write('\n'.join(report))

if __name__ == "__main__":
    main()
