
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>12_fixed_headers.csv - 数据查看器</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }
        .info {
            background-color: #e8f4fd;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        tr:nth-child(even) {
            background-color: #f8f9fa;
        }
        .stats {
            margin-top: 30px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>12_fixed_headers.csv 数据查看器</h1>
        
        <div class="info">
            <h3>文件信息</h3>
            <p><strong>数据形状:</strong> 3129 行 x 11 列</p>
            <p><strong>列名:</strong> Time, 时间, 遮阳罩表面温度, 遮阳罩背面温度, 遮阳罩皮革表面温度, 制冷帐篷表面温度, 制冷帐篷背面温度, 制冷帐篷皮革温度, 皮革表面温度, 皮革背面温度, 环境温度</p>
        </div>
        
        <h3>数据预览 (前20行)</h3>
        <table border="1" class="dataframe table" id="data-table">
  <thead>
    <tr style="text-align: right;">
      <th></th>
      <th>Time</th>
      <th>时间</th>
      <th>遮阳罩表面温度</th>
      <th>遮阳罩背面温度</th>
      <th>遮阳罩皮革表面温度</th>
      <th>制冷帐篷表面温度</th>
      <th>制冷帐篷背面温度</th>
      <th>制冷帐篷皮革温度</th>
      <th>皮革表面温度</th>
      <th>皮革背面温度</th>
      <th>环境温度</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <th>0</th>
      <td>44380.357814</td>
      <td>0.000000</td>
      <td>43.552443</td>
      <td>41.961878</td>
      <td>37.950348</td>
      <td>37.174834</td>
      <td>33.537469</td>
      <td>30.219609</td>
      <td>39.567117</td>
      <td>31.765939</td>
      <td>32.705658</td>
    </tr>
    <tr>
      <th>1</th>
      <td>0.000000</td>
      <td>10.000353</td>
      <td>43.118776</td>
      <td>41.526046</td>
      <td>37.349288</td>
      <td>37.004951</td>
      <td>33.362894</td>
      <td>30.174641</td>
      <td>39.695013</td>
      <td>30.685922</td>
      <td>31.442293</td>
    </tr>
    <tr>
      <th>2</th>
      <td>0.000000</td>
      <td>20.000650</td>
      <td>41.186684</td>
      <td>40.244428</td>
      <td>36.018618</td>
      <td>37.124985</td>
      <td>33.298660</td>
      <td>30.263771</td>
      <td>39.890741</td>
      <td>31.671143</td>
      <td>32.217210</td>
    </tr>
    <tr>
      <th>3</th>
      <td>0.000000</td>
      <td>30.000888</td>
      <td>39.665075</td>
      <td>39.711491</td>
      <td>35.465378</td>
      <td>37.438758</td>
      <td>33.475348</td>
      <td>30.411363</td>
      <td>40.310172</td>
      <td>32.675090</td>
      <td>31.460501</td>
    </tr>
    <tr>
      <th>4</th>
      <td>0.000000</td>
      <td>40.001958</td>
      <td>40.137656</td>
      <td>40.039854</td>
      <td>35.047429</td>
      <td>37.706931</td>
      <td>33.477046</td>
      <td>30.530326</td>
      <td>39.952735</td>
      <td>33.150698</td>
      <td>30.662944</td>
    </tr>
    <tr>
      <th>5</th>
      <td>0.000000</td>
      <td>50.002496</td>
      <td>40.459038</td>
      <td>40.517927</td>
      <td>34.806637</td>
      <td>38.008497</td>
      <td>33.637953</td>
      <td>30.718523</td>
      <td>40.227549</td>
      <td>33.559705</td>
      <td>31.068326</td>
    </tr>
    <tr>
      <th>6</th>
      <td>0.000000</td>
      <td>60.003561</td>
      <td>40.781079</td>
      <td>40.817676</td>
      <td>34.638546</td>
      <td>38.207076</td>
      <td>33.770536</td>
      <td>30.683318</td>
      <td>40.794718</td>
      <td>32.661401</td>
      <td>30.591773</td>
    </tr>
    <tr>
      <th>7</th>
      <td>0.000000</td>
      <td>70.003946</td>
      <td>40.984901</td>
      <td>41.150796</td>
      <td>34.462930</td>
      <td>38.248709</td>
      <td>33.713973</td>
      <td>30.687388</td>
      <td>41.122391</td>
      <td>32.243571</td>
      <td>30.625442</td>
    </tr>
    <tr>
      <th>8</th>
      <td>0.000000</td>
      <td>80.003011</td>
      <td>41.388655</td>
      <td>41.682378</td>
      <td>34.420372</td>
      <td>38.683492</td>
      <td>34.036224</td>
      <td>30.821097</td>
      <td>41.408421</td>
      <td>33.671385</td>
      <td>30.907346</td>
    </tr>
    <tr>
      <th>9</th>
      <td>0.000000</td>
      <td>90.003317</td>
      <td>42.013496</td>
      <td>42.056412</td>
      <td>34.379905</td>
      <td>39.010007</td>
      <td>34.486897</td>
      <td>30.943587</td>
      <td>40.252594</td>
      <td>34.443462</td>
      <td>31.380214</td>
    </tr>
    <tr>
      <th>10</th>
      <td>0.000000</td>
      <td>100.004436</td>
      <td>41.594608</td>
      <td>42.141693</td>
      <td>34.357178</td>
      <td>39.138537</td>
      <td>34.656415</td>
      <td>31.159078</td>
      <td>40.923180</td>
      <td>34.425080</td>
      <td>31.517676</td>
    </tr>
    <tr>
      <th>11</th>
      <td>0.000000</td>
      <td>110.004735</td>
      <td>37.702270</td>
      <td>40.164619</td>
      <td>34.266310</td>
      <td>38.098797</td>
      <td>34.052729</td>
      <td>31.008535</td>
      <td>41.335074</td>
      <td>33.900863</td>
      <td>30.249788</td>
    </tr>
    <tr>
      <th>12</th>
      <td>0.000000</td>
      <td>120.004794</td>
      <td>40.215303</td>
      <td>39.077399</td>
      <td>34.473873</td>
      <td>38.143422</td>
      <td>33.174825</td>
      <td>30.898407</td>
      <td>41.462042</td>
      <td>32.880896</td>
      <td>30.359689</td>
    </tr>
    <tr>
      <th>13</th>
      <td>0.000000</td>
      <td>130.004608</td>
      <td>42.503186</td>
      <td>40.283290</td>
      <td>35.704434</td>
      <td>38.640445</td>
      <td>33.156733</td>
      <td>30.901378</td>
      <td>41.371157</td>
      <td>35.692104</td>
      <td>31.870283</td>
    </tr>
    <tr>
      <th>14</th>
      <td>0.000000</td>
      <td>140.005014</td>
      <td>44.329393</td>
      <td>41.617806</td>
      <td>36.481614</td>
      <td>38.738081</td>
      <td>33.601769</td>
      <td>30.928384</td>
      <td>41.514449</td>
      <td>34.146159</td>
      <td>32.002444</td>
    </tr>
    <tr>
      <th>15</th>
      <td>0.000000</td>
      <td>150.006183</td>
      <td>45.141290</td>
      <td>42.470545</td>
      <td>37.122951</td>
      <td>38.411818</td>
      <td>34.368057</td>
      <td>31.379566</td>
      <td>41.545901</td>
      <td>33.186075</td>
      <td>31.832514</td>
    </tr>
    <tr>
      <th>16</th>
      <td>0.000000</td>
      <td>160.007514</td>
      <td>42.543908</td>
      <td>39.257084</td>
      <td>37.473913</td>
      <td>38.612755</td>
      <td>33.461893</td>
      <td>31.147337</td>
      <td>41.542771</td>
      <td>32.326780</td>
      <td>31.907502</td>
    </tr>
    <tr>
      <th>17</th>
      <td>0.000000</td>
      <td>170.007183</td>
      <td>43.951529</td>
      <td>41.020031</td>
      <td>37.752088</td>
      <td>38.680699</td>
      <td>33.218705</td>
      <td>31.100862</td>
      <td>41.634392</td>
      <td>32.391454</td>
      <td>31.868852</td>
    </tr>
    <tr>
      <th>18</th>
      <td>0.000000</td>
      <td>180.008055</td>
      <td>44.690499</td>
      <td>42.098700</td>
      <td>37.935662</td>
      <td>38.800593</td>
      <td>33.367350</td>
      <td>31.081713</td>
      <td>41.670622</td>
      <td>32.887356</td>
      <td>32.416765</td>
    </tr>
    <tr>
      <th>19</th>
      <td>0.000000</td>
      <td>190.008879</td>
      <td>45.293900</td>
      <td>43.050768</td>
      <td>38.134531</td>
      <td>39.003178</td>
      <td>33.551041</td>
      <td>31.146788</td>
      <td>41.674883</td>
      <td>33.431370</td>
      <td>32.496187</td>
    </tr>
  </tbody>
</table>
        
        <div class="stats">
            <h3>数值列统计</h3>
            <table border="1" class="dataframe table">
  <thead>
    <tr style="text-align: right;">
      <th></th>
      <th>Time</th>
      <th>时间</th>
      <th>遮阳罩表面温度</th>
      <th>遮阳罩背面温度</th>
      <th>遮阳罩皮革表面温度</th>
      <th>制冷帐篷表面温度</th>
      <th>制冷帐篷背面温度</th>
      <th>制冷帐篷皮革温度</th>
      <th>皮革表面温度</th>
      <th>皮革背面温度</th>
      <th>环境温度</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <th>count</th>
      <td>3129.000000</td>
      <td>3129.000000</td>
      <td>3129.000000</td>
      <td>3129.000000</td>
      <td>3129.000000</td>
      <td>3129.000000</td>
      <td>3129.000000</td>
      <td>3129.000000</td>
      <td>3129.000000</td>
      <td>3129.000000</td>
      <td>3129.000000</td>
    </tr>
    <tr>
      <th>mean</th>
      <td>14.183560</td>
      <td>15650.489038</td>
      <td>46.434822</td>
      <td>45.878299</td>
      <td>37.026064</td>
      <td>37.489745</td>
      <td>37.875141</td>
      <td>41.924536</td>
      <td>42.252981</td>
      <td>37.659252</td>
      <td>33.297135</td>
    </tr>
    <tr>
      <th>std</th>
      <td>793.392367</td>
      <td>9035.397996</td>
      <td>5.366781</td>
      <td>3.774871</td>
      <td>1.476521</td>
      <td>3.002207</td>
      <td>2.789793</td>
      <td>6.346532</td>
      <td>3.881690</td>
      <td>2.379681</td>
      <td>0.950845</td>
    </tr>
    <tr>
      <th>min</th>
      <td>0.000000</td>
      <td>0.000000</td>
      <td>33.641373</td>
      <td>35.577365</td>
      <td>32.455005</td>
      <td>32.517553</td>
      <td>32.100748</td>
      <td>30.174641</td>
      <td>34.110502</td>
      <td>30.685922</td>
      <td>30.249788</td>
    </tr>
    <tr>
      <th>25%</th>
      <td>0.000000</td>
      <td>7830.836935</td>
      <td>43.360635</td>
      <td>44.210049</td>
      <td>36.748553</td>
      <td>35.727893</td>
      <td>36.204515</td>
      <td>35.792402</td>
      <td>39.621761</td>
      <td>35.508938</td>
      <td>32.661732</td>
    </tr>
    <tr>
      <th>50%</th>
      <td>0.000000</td>
      <td>15651.105086</td>
      <td>46.807584</td>
      <td>46.378540</td>
      <td>37.361375</td>
      <td>37.152098</td>
      <td>37.793925</td>
      <td>42.025487</td>
      <td>42.446415</td>
      <td>37.697721</td>
      <td>33.301686</td>
    </tr>
    <tr>
      <th>75%</th>
      <td>0.000000</td>
      <td>23471.371660</td>
      <td>50.498117</td>
      <td>48.341529</td>
      <td>37.950348</td>
      <td>38.045206</td>
      <td>39.279784</td>
      <td>48.186126</td>
      <td>45.055544</td>
      <td>39.678636</td>
      <td>33.938202</td>
    </tr>
    <tr>
      <th>max</th>
      <td>44380.357814</td>
      <td>31291.633074</td>
      <td>58.227142</td>
      <td>55.284926</td>
      <td>39.859637</td>
      <td>50.041844</td>
      <td>45.920898</td>
      <td>52.612782</td>
      <td>51.547773</td>
      <td>43.994986</td>
      <td>36.461567</td>
    </tr>
  </tbody>
</table>
        </div>
    </div>
</body>
</html>
