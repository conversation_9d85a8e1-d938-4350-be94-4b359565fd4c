#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Excel文件转CSV工具
将12.xlsx和13.xlsx转换为CSV格式，以便进行对比分析
"""

import pandas as pd
import os
import sys

def convert_excel_to_csv(excel_file, csv_file):
    """将Excel文件转换为CSV文件"""
    try:
        print(f"正在转换 {excel_file} -> {csv_file}")
        
        # 读取Excel文件
        df = pd.read_excel(excel_file)
        
        # 显示基本信息
        print(f"  数据形状: {df.shape}")
        print(f"  列名: {df.columns.tolist()}")
        
        # 保存为CSV
        df.to_csv(csv_file, index=False, encoding='utf-8-sig')
        print(f"  转换完成: {csv_file}")
        
        return True
        
    except Exception as e:
        print(f"  转换失败: {e}")
        return False

def main():
    """主函数"""
    print("Excel转CSV工具")
    print("=" * 50)
    
    # 要转换的文件列表
    conversions = [
        ('12.xlsx', '12_fixed_headers.csv'),
        ('13.xlsx', '13_fixed_headers.csv')
    ]
    
    success_count = 0
    
    for excel_file, csv_file in conversions:
        if os.path.exists(excel_file):
            if convert_excel_to_csv(excel_file, csv_file):
                success_count += 1
        else:
            print(f"文件不存在: {excel_file}")
    
    print("\n" + "=" * 50)
    print(f"转换完成: {success_count}/{len(conversions)} 个文件成功转换")
    
    if success_count == len(conversions):
        print("所有文件转换成功！现在可以进行四文件对比分析。")
    else:
        print("部分文件转换失败，请检查错误信息。")

if __name__ == "__main__":
    main()
