#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试结果对比绘图工具
基于现有GUI程序修改，专门用于对比测试11和测试5
"""

import tkinter as tk
from tkinter import ttk, messagebox
import os
import sys

# 依赖检查
def check_dependencies():
    """检查必要的依赖包"""
    missing_deps = []
    
    try:
        import pandas as pd
        print("✓ pandas 可用")
    except ImportError:
        missing_deps.append("pandas")
    
    try:
        import matplotlib.pyplot as plt
        import matplotlib
        print("✓ matplotlib 可用")
    except ImportError:
        missing_deps.append("matplotlib")
    
    try:
        import numpy as np
        print("✓ numpy 可用")
    except ImportError:
        missing_deps.append("numpy")
    
    return missing_deps

class ComparisonPlotter:
    """测试结果对比绘图器"""
    
    def __init__(self, root):
        self.root = root
        self.root.title("测试11 vs 测试5 - 折线图对比工具")
        self.root.geometry("800x600")
        
        # 检查依赖
        missing_deps = check_dependencies()
        if missing_deps:
            self.show_dependency_error(missing_deps)
            return
        
        # 导入依赖包
        try:
            import pandas as pd
            import matplotlib.pyplot as plt
            from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
            import matplotlib
            matplotlib.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial']
            matplotlib.rcParams['axes.unicode_minus'] = False
            
            self.pd = pd
            self.plt = plt
            self.FigureCanvasTkAgg = FigureCanvasTkAgg
            
        except ImportError as e:
            messagebox.showerror("导入错误", f"无法导入必要的库: {e}")
            return
        
        self.setup_ui()
        self.load_data()
    
    def show_dependency_error(self, missing_deps):
        """显示依赖包缺失错误"""
        error_msg = f"缺少以下依赖包: {', '.join(missing_deps)}\n\n"
        error_msg += "请安装这些包后重试:\n"
        for dep in missing_deps:
            error_msg += f"pip install {dep}\n"
        
        messagebox.showerror("依赖包缺失", error_msg)
        self.root.destroy()
    
    def setup_ui(self):
        """设置用户界面"""
        # 主标题
        title_label = ttk.Label(self.root, text="测试11 vs 测试5 - 折线图对比分析", 
                               font=("Arial", 16, "bold"))
        title_label.pack(pady=20)
        
        # 状态显示
        self.status_var = tk.StringVar(value="正在初始化...")
        status_label = ttk.Label(self.root, textvariable=self.status_var)
        status_label.pack(pady=10)
        
        # 按钮框架
        button_frame = ttk.Frame(self.root)
        button_frame.pack(pady=20)
        
        # 生成对比图按钮
        ttk.Button(button_frame, text="生成完整对比图", 
                  command=self.create_full_comparison, width=20).pack(side=tk.LEFT, padx=10)
        
        ttk.Button(button_frame, text="生成关键参数对比", 
                  command=self.create_key_comparison, width=20).pack(side=tk.LEFT, padx=10)
        
        ttk.Button(button_frame, text="生成制冷效果对比", 
                  command=self.create_cooling_comparison, width=20).pack(side=tk.LEFT, padx=10)
        
        # 信息显示区域
        info_frame = ttk.LabelFrame(self.root, text="数据信息", padding=10)
        info_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        self.info_text = tk.Text(info_frame, height=15, wrap=tk.WORD)
        scrollbar = ttk.Scrollbar(info_frame, orient=tk.VERTICAL, command=self.info_text.yview)
        self.info_text.configure(yscrollcommand=scrollbar.set)
        
        self.info_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    def load_data(self):
        """加载测试数据"""
        try:
            self.status_var.set("正在加载数据...")
            
            # 检查文件是否存在
            if not os.path.exists('11_fixed_headers.csv'):
                raise FileNotFoundError("找不到文件: 11_fixed_headers.csv")
            if not os.path.exists('5_fixed_headers.csv'):
                raise FileNotFoundError("找不到文件: 5_fixed_headers.csv")
            
            # 读取数据
            self.df11 = self.pd.read_csv('11_fixed_headers.csv')
            self.df5 = self.pd.read_csv('5_fixed_headers.csv')
            
            # 显示数据信息
            info_text = "数据加载成功！\n\n"
            info_text += f"测试11数据点数量: {len(self.df11):,} 个\n"
            info_text += f"测试5数据点数量: {len(self.df5):,} 个\n"
            info_text += f"测试11持续时间: {self.df11['时间'].max():.1f} 秒 ({self.df11['时间'].max()/60:.1f} 分钟)\n"
            info_text += f"测试5持续时间: {self.df5['时间'].max():.1f} 秒 ({self.df5['时间'].max()/60:.1f} 分钟)\n\n"
            
            # 温度参数列表
            self.temp_columns = [
                '遮阳罩表面温度', '遮阳罩背面温度', '遮阳罩皮革表面温度',
                '制冷帐篷表面温度', '制冷帐篷背面温度', '制冷帐篷皮革温度',
                '皮革表面温度', '皮革背面温度', '环境温度'
            ]
            
            info_text += "可用的温度参数:\n"
            for i, col in enumerate(self.temp_columns, 1):
                info_text += f"{i}. {col}\n"
            
            info_text += "\n点击上方按钮生成对比图表。"
            
            self.info_text.delete(1.0, tk.END)
            self.info_text.insert(tk.END, info_text)
            
            self.status_var.set("数据加载完成，可以生成对比图")
            
        except Exception as e:
            error_msg = f"数据加载失败: {e}"
            self.status_var.set(error_msg)
            messagebox.showerror("错误", error_msg)
    
    def create_full_comparison(self):
        """创建完整的9参数对比图"""
        try:
            self.status_var.set("正在生成完整对比图...")
            
            # 创建图表
            fig, axes = self.plt.subplots(3, 3, figsize=(18, 14))
            fig.suptitle('测试11 vs 测试5 - 完整温度对比折线图', fontsize=16, fontweight='bold')
            
            colors = {'11': '#FF6B6B', '5': '#4ECDC4'}
            
            for i, col in enumerate(self.temp_columns):
                row = i // 3
                col_idx = i % 3
                ax = axes[row, col_idx]
                
                # 绘制折线
                ax.plot(self.df11['时间']/60, self.df11[col], 
                       color=colors['11'], linewidth=2, alpha=0.8, label='测试11')
                ax.plot(self.df5['时间']/60, self.df5[col], 
                       color=colors['5'], linewidth=2, alpha=0.8, label='测试5')
                
                ax.set_title(col, fontsize=11, fontweight='bold')
                ax.set_xlabel('时间 (分钟)', fontsize=9)
                ax.set_ylabel('温度 (°C)', fontsize=9)
                ax.legend(fontsize=8)
                ax.grid(True, alpha=0.3)
                
                # 添加平均值差异
                avg_11 = self.df11[col].mean()
                avg_5 = self.df5[col].mean()
                diff = avg_11 - avg_5
                ax.text(0.02, 0.98, f'平均差: {diff:+.1f}°C', 
                       transform=ax.transAxes, fontsize=8,
                       bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.7),
                       verticalalignment='top')
            
            self.plt.tight_layout()
            self.plt.subplots_adjust(top=0.93)
            
            # 保存图片
            filename = 'complete_comparison.png'
            self.plt.savefig(filename, dpi=300, bbox_inches='tight')
            self.plt.show()
            
            self.status_var.set(f"完整对比图已保存: {filename}")
            messagebox.showinfo("成功", f"完整对比图已保存为: {filename}")
            
        except Exception as e:
            error_msg = f"生成完整对比图失败: {e}"
            self.status_var.set(error_msg)
            messagebox.showerror("错误", error_msg)
    
    def create_key_comparison(self):
        """创建关键参数对比图"""
        try:
            self.status_var.set("正在生成关键参数对比图...")
            
            key_params = ['环境温度', '遮阳罩表面温度', '制冷帐篷表面温度', '皮革表面温度']
            
            fig, axes = self.plt.subplots(2, 2, figsize=(14, 10))
            fig.suptitle('关键温度参数对比 - 测试11 vs 测试5', fontsize=14, fontweight='bold')
            
            colors = {'11': '#FF6B6B', '5': '#4ECDC4'}
            
            for i, param in enumerate(key_params):
                row = i // 2
                col = i % 2
                ax = axes[row, col]
                
                ax.plot(self.df11['时间']/60, self.df11[param], 
                       color=colors['11'], linewidth=3, alpha=0.9, 
                       label='测试11', marker='o', markersize=1)
                ax.plot(self.df5['时间']/60, self.df5[param], 
                       color=colors['5'], linewidth=3, alpha=0.9, 
                       label='测试5', marker='s', markersize=1)
                
                ax.set_title(param, fontsize=12, fontweight='bold')
                ax.set_xlabel('时间 (分钟)', fontsize=10)
                ax.set_ylabel('温度 (°C)', fontsize=10)
                ax.legend(fontsize=10)
                ax.grid(True, alpha=0.4)
                
                # 添加统计信息
                start_11 = self.df11[param].iloc[0]
                end_11 = self.df11[param].iloc[-1]
                start_5 = self.df5[param].iloc[0]
                end_5 = self.df5[param].iloc[-1]
                
                change_11 = end_11 - start_11
                change_5 = end_5 - start_5
                
                stats_text = f'测试11: {start_11:.1f}→{end_11:.1f}°C ({change_11:+.1f})\n'
                stats_text += f'测试5: {start_5:.1f}→{end_5:.1f}°C ({change_5:+.1f})'
                
                ax.text(0.02, 0.98, stats_text, transform=ax.transAxes, fontsize=8,
                       bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8),
                       verticalalignment='top')
            
            self.plt.tight_layout()
            self.plt.subplots_adjust(top=0.93)
            
            filename = 'key_comparison.png'
            self.plt.savefig(filename, dpi=300, bbox_inches='tight')
            self.plt.show()
            
            self.status_var.set(f"关键参数对比图已保存: {filename}")
            messagebox.showinfo("成功", f"关键参数对比图已保存为: {filename}")
            
        except Exception as e:
            error_msg = f"生成关键参数对比图失败: {e}"
            self.status_var.set(error_msg)
            messagebox.showerror("错误", error_msg)
    
    def create_cooling_comparison(self):
        """创建制冷效果对比图"""
        try:
            self.status_var.set("正在生成制冷效果对比图...")
            
            fig, (ax1, ax2) = self.plt.subplots(1, 2, figsize=(16, 7))
            fig.suptitle('制冷效果专项对比分析', fontsize=14, fontweight='bold')
            
            colors = {'11': '#FF6B6B', '5': '#4ECDC4'}
            
            # 左图：制冷帐篷温度
            ax1.plot(self.df11['时间']/60, self.df11['制冷帐篷表面温度'], 
                    color=colors['11'], linewidth=3, label='测试11-表面', alpha=0.9)
            ax1.plot(self.df11['时间']/60, self.df11['制冷帐篷背面温度'], 
                    color=colors['11'], linewidth=2, linestyle='--', label='测试11-背面', alpha=0.7)
            ax1.plot(self.df5['时间']/60, self.df5['制冷帐篷表面温度'], 
                    color=colors['5'], linewidth=3, label='测试5-表面', alpha=0.9)
            ax1.plot(self.df5['时间']/60, self.df5['制冷帐篷背面温度'], 
                    color=colors['5'], linewidth=2, linestyle='--', label='测试5-背面', alpha=0.7)
            
            ax1.set_title('制冷帐篷温度对比', fontsize=12, fontweight='bold')
            ax1.set_xlabel('时间 (分钟)', fontsize=10)
            ax1.set_ylabel('温度 (°C)', fontsize=10)
            ax1.legend(fontsize=9)
            ax1.grid(True, alpha=0.3)
            
            # 右图：皮革温度
            ax2.plot(self.df11['时间']/60, self.df11['皮革表面温度'], 
                    color=colors['11'], linewidth=3, label='测试11-表面', alpha=0.9)
            ax2.plot(self.df11['时间']/60, self.df11['皮革背面温度'], 
                    color=colors['11'], linewidth=2, linestyle='--', label='测试11-背面', alpha=0.7)
            ax2.plot(self.df5['时间']/60, self.df5['皮革表面温度'], 
                    color=colors['5'], linewidth=3, label='测试5-表面', alpha=0.9)
            ax2.plot(self.df5['时间']/60, self.df5['皮革背面温度'], 
                    color=colors['5'], linewidth=2, linestyle='--', label='测试5-背面', alpha=0.7)
            
            ax2.set_title('皮革温度对比', fontsize=12, fontweight='bold')
            ax2.set_xlabel('时间 (分钟)', fontsize=10)
            ax2.set_ylabel('温度 (°C)', fontsize=10)
            ax2.legend(fontsize=9)
            ax2.grid(True, alpha=0.3)
            
            self.plt.tight_layout()
            self.plt.subplots_adjust(top=0.9)
            
            filename = 'cooling_effect_comparison.png'
            self.plt.savefig(filename, dpi=300, bbox_inches='tight')
            self.plt.show()
            
            self.status_var.set(f"制冷效果对比图已保存: {filename}")
            messagebox.showinfo("成功", f"制冷效果对比图已保存为: {filename}")
            
        except Exception as e:
            error_msg = f"生成制冷效果对比图失败: {e}"
            self.status_var.set(error_msg)
            messagebox.showerror("错误", error_msg)

def main():
    """主函数"""
    root = tk.Tk()
    app = ComparisonPlotter(root)
    root.mainloop()

if __name__ == "__main__":
    main()
