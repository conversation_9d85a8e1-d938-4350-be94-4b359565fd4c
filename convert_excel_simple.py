#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的Excel转CSV工具
"""

import pandas as pd
import os

def convert_file(excel_file, csv_file):
    """转换单个Excel文件为CSV"""
    try:
        print(f"正在转换 {excel_file}...")
        
        # 尝试多种方法读取Excel
        df = None
        
        # 方法1: 默认引擎
        try:
            df = pd.read_excel(excel_file)
            print(f"  使用默认引擎成功读取")
        except Exception as e1:
            print(f"  默认引擎失败: {e1}")
            
            # 方法2: openpyxl引擎
            try:
                df = pd.read_excel(excel_file, engine='openpyxl')
                print(f"  使用openpyxl引擎成功读取")
            except Exception as e2:
                print(f"  openpyxl引擎失败: {e2}")
                
                # 方法3: xlrd引擎
                try:
                    df = pd.read_excel(excel_file, engine='xlrd')
                    print(f"  使用xlrd引擎成功读取")
                except Exception as e3:
                    print(f"  xlrd引擎失败: {e3}")
                    return False
        
        if df is not None:
            # 显示数据信息
            print(f"  数据形状: {df.shape}")
            print(f"  列名: {list(df.columns)}")
            
            # 保存为CSV
            df.to_csv(csv_file, index=False, encoding='utf-8-sig')
            print(f"  成功保存为: {csv_file}")
            return True
        else:
            print(f"  所有方法都失败了")
            return False
            
    except Exception as e:
        print(f"  转换失败: {e}")
        return False

def main():
    """主函数"""
    print("Excel转CSV工具")
    print("=" * 40)
    
    # 要转换的文件
    files_to_convert = [
        ('12.xlsx', '12_fixed_headers.csv'),
        ('13.xlsx', '13_fixed_headers.csv')
    ]
    
    success_count = 0
    
    for excel_file, csv_file in files_to_convert:
        if os.path.exists(excel_file):
            if convert_file(excel_file, csv_file):
                success_count += 1
            print()
        else:
            print(f"文件不存在: {excel_file}")
            print()
    
    print("=" * 40)
    print(f"转换结果: {success_count}/{len(files_to_convert)} 个文件成功")
    
    if success_count > 0:
        print("现在可以运行四文件对比分析了！")

if __name__ == "__main__":
    main()
