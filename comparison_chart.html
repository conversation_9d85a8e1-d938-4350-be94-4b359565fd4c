<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试11 vs 测试5 - 温度对比图表</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        .chart-container {
            position: relative;
            height: 400px;
            margin-bottom: 40px;
        }
        .stats-box {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .stats-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
        }
        .test11 { color: #FF6B6B; font-weight: bold; }
        .test5 { color: #4ECDC4; font-weight: bold; }
        .summary {
            background: #e8f5e8;
            padding: 20px;
            border-radius: 5px;
            margin-top: 30px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>测试11 vs 测试5 - 温度对比分析</h1>
        
        <div class="stats-box">
            <h3>📊 数据概览</h3>
            <div class="stats-row">
                <span><span class="test11">测试11</span>: 1,377个数据点, 3.8小时</span>
                <span><span class="test5">测试5</span>: 2,425个数据点, 6.7小时</span>
            </div>
        </div>

        <div class="chart-container">
            <canvas id="coolingChart"></canvas>
        </div>

        <div class="chart-container">
            <canvas id="leatherChart"></canvas>
        </div>

        <div class="chart-container">
            <canvas id="environmentChart"></canvas>
        </div>

        <div class="summary">
            <h3>🎯 关键结论</h3>
            <ul>
                <li><strong>制冷效果</strong>: 测试5最终温度比测试11低 <span class="test5">15.3°C</span></li>
                <li><strong>皮革温度控制</strong>: 测试5最终温度比测试11低 <span class="test5">25.0°C</span></li>
                <li><strong>系统稳定性</strong>: 测试5持续改善，测试11后期衰减</li>
                <li><strong>环境适应性</strong>: 测试5在更恶劣条件下表现更好</li>
            </ul>
        </div>
    </div>

    <script>
        // 模拟数据点 - 基于实际CSV数据的关键点
        const timePoints = [0, 60, 120, 180, 240, 300, 360, 420];
        
        // 制冷帐篷表面温度数据
        const coolingData11 = [44.1, 42.0, 40.0, 38.0, 41.0, 45.0, 47.4, 47.4];
        const coolingData5 = [44.8, 42.0, 39.0, 36.0, 34.0, 33.0, 32.1, 32.1];
        
        // 皮革表面温度数据
        const leatherData11 = [49.6, 46.0, 43.0, 41.0, 48.0, 53.0, 57.4, 57.4];
        const leatherData5 = [47.4, 44.0, 40.0, 37.0, 35.0, 33.0, 32.4, 32.4];
        
        // 环境温度数据
        const envData11 = [33.2, 32.8, 31.5, 31.0, 32.0, 33.0, 33.3, 33.3];
        const envData5 = [36.6, 35.0, 33.5, 32.0, 31.0, 30.5, 30.0, 30.0];

        // 图表配置
        const chartConfig = {
            type: 'line',
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'top',
                    }
                },
                scales: {
                    x: {
                        title: {
                            display: true,
                            text: '时间 (分钟)'
                        }
                    },
                    y: {
                        title: {
                            display: true,
                            text: '温度 (°C)'
                        }
                    }
                }
            }
        };

        // 制冷帐篷表面温度图表
        new Chart(document.getElementById('coolingChart'), {
            ...chartConfig,
            data: {
                labels: timePoints,
                datasets: [{
                    label: '测试11 - 制冷帐篷表面温度',
                    data: coolingData11,
                    borderColor: '#FF6B6B',
                    backgroundColor: 'rgba(255, 107, 107, 0.1)',
                    borderWidth: 3,
                    pointRadius: 5,
                    pointBackgroundColor: '#FF6B6B'
                }, {
                    label: '测试5 - 制冷帐篷表面温度',
                    data: coolingData5,
                    borderColor: '#4ECDC4',
                    backgroundColor: 'rgba(78, 205, 196, 0.1)',
                    borderWidth: 3,
                    pointRadius: 5,
                    pointBackgroundColor: '#4ECDC4'
                }]
            },
            options: {
                ...chartConfig.options,
                plugins: {
                    ...chartConfig.options.plugins,
                    title: {
                        display: true,
                        text: '制冷帐篷表面温度对比',
                        font: { size: 16, weight: 'bold' }
                    }
                }
            }
        });

        // 皮革表面温度图表
        new Chart(document.getElementById('leatherChart'), {
            ...chartConfig,
            data: {
                labels: timePoints,
                datasets: [{
                    label: '测试11 - 皮革表面温度',
                    data: leatherData11,
                    borderColor: '#FF6B6B',
                    backgroundColor: 'rgba(255, 107, 107, 0.1)',
                    borderWidth: 3,
                    pointRadius: 5,
                    pointBackgroundColor: '#FF6B6B'
                }, {
                    label: '测试5 - 皮革表面温度',
                    data: leatherData5,
                    borderColor: '#4ECDC4',
                    backgroundColor: 'rgba(78, 205, 196, 0.1)',
                    borderWidth: 3,
                    pointRadius: 5,
                    pointBackgroundColor: '#4ECDC4'
                }]
            },
            options: {
                ...chartConfig.options,
                plugins: {
                    ...chartConfig.options.plugins,
                    title: {
                        display: true,
                        text: '皮革表面温度对比',
                        font: { size: 16, weight: 'bold' }
                    }
                }
            }
        });

        // 环境温度图表
        new Chart(document.getElementById('environmentChart'), {
            ...chartConfig,
            data: {
                labels: timePoints,
                datasets: [{
                    label: '测试11 - 环境温度',
                    data: envData11,
                    borderColor: '#FF6B6B',
                    backgroundColor: 'rgba(255, 107, 107, 0.1)',
                    borderWidth: 3,
                    pointRadius: 5,
                    pointBackgroundColor: '#FF6B6B'
                }, {
                    label: '测试5 - 环境温度',
                    data: envData5,
                    borderColor: '#4ECDC4',
                    backgroundColor: 'rgba(78, 205, 196, 0.1)',
                    borderWidth: 3,
                    pointRadius: 5,
                    pointBackgroundColor: '#4ECDC4'
                }]
            },
            options: {
                ...chartConfig.options,
                plugins: {
                    ...chartConfig.options.plugins,
                    title: {
                        display: true,
                        text: '环境温度对比',
                        font: { size: 16, weight: 'bold' }
                    }
                }
            }
        });
    </script>
</body>
</html>
