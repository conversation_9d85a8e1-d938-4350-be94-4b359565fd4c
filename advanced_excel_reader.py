#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高级Excel文件读取器
尝试多种不同的方法读取Excel文件
"""

import os
import sys
import csv
import json
from pathlib import Path

def method1_openpyxl_direct(filename):
    """方法1: 直接使用openpyxl读取"""
    try:
        import openpyxl
        print(f"  方法1: 使用openpyxl直接读取...")
        
        # 打开工作簿
        wb = openpyxl.load_workbook(filename, data_only=True)
        ws = wb.active
        
        # 获取所有数据
        data = []
        for row in ws.iter_rows(values_only=True):
            if any(cell is not None for cell in row):  # 跳过空行
                data.append(row)
        
        wb.close()
        
        if data:
            print(f"    成功读取 {len(data)} 行数据")
            return data
        else:
            print(f"    没有找到数据")
            return None
            
    except ImportError:
        print(f"    openpyxl库不可用")
        return None
    except Exception as e:
        print(f"    失败: {e}")
        return None

def method2_xlrd(filename):
    """方法2: 使用xlrd读取"""
    try:
        import xlrd
        print(f"  方法2: 使用xlrd读取...")
        
        # 打开工作簿
        wb = xlrd.open_workbook(filename)
        ws = wb.sheet_by_index(0)
        
        # 获取所有数据
        data = []
        for row_idx in range(ws.nrows):
            row = []
            for col_idx in range(ws.ncols):
                cell = ws.cell(row_idx, col_idx)
                row.append(cell.value)
            data.append(tuple(row))
        
        if data:
            print(f"    成功读取 {len(data)} 行数据")
            return data
        else:
            print(f"    没有找到数据")
            return None
            
    except ImportError:
        print(f"    xlrd库不可用")
        return None
    except Exception as e:
        print(f"    失败: {e}")
        return None

def method3_pyexcel(filename):
    """方法3: 使用pyexcel读取"""
    try:
        import pyexcel
        print(f"  方法3: 使用pyexcel读取...")
        
        # 读取数据
        sheet = pyexcel.get_sheet(file_name=filename)
        data = []
        for row in sheet:
            data.append(tuple(row))
        
        if data:
            print(f"    成功读取 {len(data)} 行数据")
            return data
        else:
            print(f"    没有找到数据")
            return None
            
    except ImportError:
        print(f"    pyexcel库不可用")
        return None
    except Exception as e:
        print(f"    失败: {e}")
        return None

def method4_zipfile_xml(filename):
    """方法4: 使用zipfile直接解析xlsx文件"""
    try:
        import zipfile
        import xml.etree.ElementTree as ET
        print(f"  方法4: 使用zipfile解析xlsx结构...")
        
        data = []
        
        with zipfile.ZipFile(filename, 'r') as zip_file:
            # 读取共享字符串
            shared_strings = []
            try:
                with zip_file.open('xl/sharedStrings.xml') as f:
                    tree = ET.parse(f)
                    root = tree.getroot()
                    for si in root.findall('.//{http://schemas.openxmlformats.org/spreadsheetml/2006/main}si'):
                        t = si.find('.//{http://schemas.openxmlformats.org/spreadsheetml/2006/main}t')
                        if t is not None:
                            shared_strings.append(t.text)
                        else:
                            shared_strings.append('')
            except:
                pass
            
            # 读取工作表数据
            with zip_file.open('xl/worksheets/sheet1.xml') as f:
                tree = ET.parse(f)
                root = tree.getroot()
                
                rows = {}
                for cell in root.findall('.//{http://schemas.openxmlformats.org/spreadsheetml/2006/main}c'):
                    ref = cell.get('r')
                    if ref:
                        # 解析单元格引用 (如 A1, B2)
                        col_str = ''.join(c for c in ref if c.isalpha())
                        row_num = int(''.join(c for c in ref if c.isdigit()))
                        
                        # 转换列字母为数字
                        col_num = 0
                        for c in col_str:
                            col_num = col_num * 26 + (ord(c) - ord('A') + 1)
                        
                        # 获取单元格值
                        v = cell.find('.//{http://schemas.openxmlformats.org/spreadsheetml/2006/main}v')
                        if v is not None:
                            cell_type = cell.get('t')
                            if cell_type == 's':  # 共享字符串
                                try:
                                    value = shared_strings[int(v.text)]
                                except:
                                    value = v.text
                            else:
                                try:
                                    value = float(v.text)
                                    if value.is_integer():
                                        value = int(value)
                                except:
                                    value = v.text
                        else:
                            value = ''
                        
                        if row_num not in rows:
                            rows[row_num] = {}
                        rows[row_num][col_num] = value
                
                # 转换为列表格式
                if rows:
                    max_row = max(rows.keys())
                    max_col = max(max(row.keys()) if row else [0] for row in rows.values())
                    
                    for row_num in range(1, max_row + 1):
                        row_data = []
                        for col_num in range(1, max_col + 1):
                            value = rows.get(row_num, {}).get(col_num, '')
                            row_data.append(value)
                        data.append(tuple(row_data))
        
        if data:
            print(f"    成功读取 {len(data)} 行数据")
            return data
        else:
            print(f"    没有找到数据")
            return None
            
    except Exception as e:
        print(f"    失败: {e}")
        return None

def method5_xlwings(filename):
    """方法5: 使用xlwings读取"""
    try:
        import xlwings as xw
        print(f"  方法5: 使用xlwings读取...")
        
        app = xw.App(visible=False)
        wb = app.books.open(filename)
        ws = wb.sheets[0]
        
        # 获取使用的范围
        used_range = ws.used_range
        if used_range:
            data = used_range.value
            if isinstance(data[0], (list, tuple)):
                data = [tuple(row) for row in data]
            else:
                data = [tuple([data])]
        else:
            data = []
        
        wb.close()
        app.quit()
        
        if data:
            print(f"    成功读取 {len(data)} 行数据")
            return data
        else:
            print(f"    没有找到数据")
            return None
            
    except ImportError:
        print(f"    xlwings库不可用")
        return None
    except Exception as e:
        print(f"    失败: {e}")
        return None

def save_data_as_csv(data, csv_filename):
    """将数据保存为CSV文件"""
    try:
        with open(csv_filename, 'w', newline='', encoding='utf-8-sig') as f:
            writer = csv.writer(f)
            for row in data:
                writer.writerow(row)
        print(f"    成功保存为: {csv_filename}")
        return True
    except Exception as e:
        print(f"    保存CSV失败: {e}")
        return False

def analyze_excel_file(filename):
    """分析Excel文件并尝试多种读取方法"""
    print(f"\n分析文件: {filename}")
    print("=" * 60)
    
    if not os.path.exists(filename):
        print(f"文件不存在: {filename}")
        return None
    
    # 显示文件信息
    file_size = os.path.getsize(filename)
    print(f"文件大小: {file_size:,} 字节")
    
    # 尝试各种方法
    methods = [
        method1_openpyxl_direct,
        method4_zipfile_xml,  # 将zipfile方法提前，因为它不依赖外部库
        method2_xlrd,
        method3_pyexcel,
        method5_xlwings,
    ]
    
    for method in methods:
        try:
            data = method(filename)
            if data:
                # 显示数据预览
                print(f"    数据预览 (前3行):")
                for i, row in enumerate(data[:3]):
                    print(f"      行{i+1}: {row[:5]}...")  # 只显示前5列
                
                # 保存为CSV
                csv_filename = filename.replace('.xlsx', '_extracted.csv')
                if save_data_as_csv(data, csv_filename):
                    return data, csv_filename
                
        except Exception as e:
            print(f"    方法执行异常: {e}")
    
    print(f"所有方法都失败了")
    return None

def main():
    """主函数"""
    print("高级Excel文件读取器")
    print("=" * 80)
    
    files_to_process = ['12.xlsx', '13.xlsx']
    successful_files = {}
    
    for filename in files_to_process:
        result = analyze_excel_file(filename)
        if result:
            data, csv_filename = result
            successful_files[filename] = {
                'data': data,
                'csv_file': csv_filename,
                'rows': len(data)
            }
    
    print("\n" + "=" * 80)
    print("处理结果总结:")
    
    if successful_files:
        print(f"成功处理 {len(successful_files)} 个文件:")
        for filename, info in successful_files.items():
            print(f"  ✓ {filename} -> {info['csv_file']} ({info['rows']} 行)")
        
        print("\n现在可以运行完整的四文件对比分析了！")
        
        # 自动运行四文件对比分析
        try:
            print("\n正在运行四文件对比分析...")
            os.system('py four_files_comparison.py')
        except Exception as e:
            print(f"自动运行对比分析失败: {e}")
            print("请手动运行: py four_files_comparison.py")
    else:
        print("没有成功处理任何文件")
        print("建议尝试其他方法或检查文件格式")

if __name__ == "__main__":
    main()
